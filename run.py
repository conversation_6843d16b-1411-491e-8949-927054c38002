import asyncio
import logging

from dotenv import load_dotenv
from livekit.agents import (
    AutoSubscribe,
    JobContext,
    JobRequest,
    WorkerOptions,
    WorkerType,
    cli,
)
from livekit.plugins import deepgram, noise_cancellation, openai, silero

from app.agents.coordinator import Coordinator
from app.constants.env_constants import DEEPGRAM_API_KEY, DEFAULT_VOICE, OPENAI_API_KEY
from app.core.logger_setup import logger


def prewarm_models(proc):
    logger.info("Prewarming models...")
    try:
        proc.userdata["vad"] = silero.VAD.load(
            min_speech_duration=0.2, min_silence_duration=0.8, activation_threshold=0.7
        )
        logger.info("VAD model prewarmed.")
    except Exception as e:
        logger.error(f"Failed to prewarm VAD model: {e}", exc_info=True)

    try:
        _ = openai.TTS(api_key=OPENAI_API_KEY, model="tts-1", voice=DEFAULT_VOICE)
        logger.info("OpenAI TTS prewarmed.")
    except Exception as e:
        logger.warning(f"Could not prewarm OpenAI TTS: {e}")

    try:
        _ = deepgram.STT(api_key=DEEPGRAM_API_KEY, language="en-US")
        logger.info("Deepgram STT prewarmed.")
    except Exception as e:
        logger.warning(f"Could not prewarm Deepgram STT: {e}")

    try:
        _ = noise_cancellation.BVC()
        logger.info("BVC Noise Cancellation prewarmed.")
    except Exception as e:
        logger.warning(f"Could not prewarm BVC Noise Cancellation: {e}")


async def entrypoint(ctx: JobContext):
    logger.info(f"Entrypoint started for room: {ctx.room.name}, Job ID: {ctx.job.id}")
    await ctx.connect(auto_subscribe=AutoSubscribe.AUDIO_ONLY)

    coordinator = Coordinator()
    await coordinator.route(ctx)


async def request_fnc(req: JobRequest):
    logger.info(f"Received job request: {req.job.id} for room {req.room.name}")
    identity = "houdini-assistant-agent"
    await req.accept(identity=identity, name="MyHoudini Assistant")
    logger.info(f"Accepted job request {req.job.id} with agent identity '{identity}'")


if __name__ == "__main__":
    load_dotenv()
    if not OPENAI_API_KEY:
        logger.error("OPENAI_API_KEY not found.")
    if not DEEPGRAM_API_KEY:
        logger.error("DEEPGRAM_API_KEY not found")

    worker_options = WorkerOptions(
        entrypoint_fnc=entrypoint,
        request_fnc=request_fnc,
        worker_type=WorkerType.ROOM,
        prewarm_fnc=prewarm_models,
    )
    cli.run_app(worker_options)
