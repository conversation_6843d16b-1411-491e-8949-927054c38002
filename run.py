import asyncio
import json
import logging
import os
import re
from collections.abc import AsyncIterable
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, Union

from livekit import rtc
from livekit.agents import (
    Agent,
    AgentSession,
    AutoSubscribe,
    ChatContext,
    FunctionTool,
    JobContext,
    JobProcess,
    JobRequest,
    ModelSettings,
    RoomInputOptions,
    RoomOutputOptions,
    StopResponse,
    WorkerOptions,
    WorkerType,
    cli,
    llm,
    stt,
    tokenize,
    tts,
    vad,
)
from livekit.plugins import deepgram, google, noise_cancellation, openai, silero
from livekit.plugins.turn_detector.multilingual import MultilingualModel

# import all function from appointement tools here
from app.assistant_tools.appointement_tools import (  # add_contact_for_appointment,; confirm_contact_for_appointment,; find_contact_for_appointment,
    apply_appointment_updates,
    create_appointment,
    delete_appointment,
    find_appointment,
    handle_appointment_update_flow,
    list_all_appointments,
    select_appointment_for_update,
)
from app.assistant_tools.contact_tools import (
    add_new_contact,
    complete_contact_onboarding_flow,
    confirm_onboarding_contact_from_multiple,
    get_contact_state,
    save_or_create_onboarding_contact,
    search_contact_for_onboarding,
    skip_contact_onboarding_flow,
    sync_contacts,
    update_existing_contact,
)

# import all function from reminder tools here
from app.assistant_tools.reminders_tools import (
    delete_reminder_tool,
    find_reminder,
    list_all_reminders,
    set_reminder,
    update_existing_reminder,
)
from app.assistant_tools.user_tools import (
    check_onboarding_status,
    get_user_state,
    play_all_voice_samples,
    save_user_info,
    set_birth_day_and_month,
    set_daily_updates_time,
    set_location_details,
    set_phone_number,
    set_preferred_name,
    set_user_name,
    set_voice,
    start_voice_onboarding,
    validate_zip_code,
)
from app.assistant_tools.weather_tools import get_weather, morning_greeting
from app.constants.action_types import FlowTrigger
from app.constants.env_constants import (
    DEEPGRAM_API_KEY,
    DEFAULT_VOICE,
    GEMINI_API_KEY,
    LOG_LEVEL,
    OPENAI_API_KEY,
    OPENAI_MODEL_NAME,
    GOOGLE_CLOUD_PROJECT,
    GOOGLE_CLOUD_LOCATION,
    GEMINI_MODEL_LLM,
)
from app.core.logger_setup import logger
from app.core.pronunciation_rules import STATIC_PRONUNCIATIONS
from app.prompts.assistant import chat_assistant
from app.providers.stt import create_resilient_stt
from app.providers.tts.factory import DEFAULT_PROVIDER, get_available_voices
from app.services import (
    appointment_api_service,
    contact_api_service,
    reminder_api_service,
    user_api_service,
    voice_api_service,
)
from app.services.contact_api_service import get_myhoudini_contacts_relationships
from app.state.contact_state import CONTACT_UPDATE_RELATIONS
from app.state.session_data import START_ONBOARDING_ASSISTANT_DIALOG, SessionData

# --- Define the Agent ---
MAX_CONTEXT_ITEMS = 20

current_relation_for_prompt = None


class MyHoudiniAgent(Agent):
    def __init__(self) -> None:
        super().__init__(
            instructions=chat_assistant,
            tools=[
                get_contact_state,
                get_user_state,
                check_onboarding_status,
                start_voice_onboarding,
                set_user_name,
                set_preferred_name,
                set_phone_number,
                set_location_details,
                set_birth_day_and_month,
                set_voice,
                play_all_voice_samples,
                save_user_info,
                set_daily_updates_time,
                sync_contacts,
                confirm_onboarding_contact_from_multiple,
                save_or_create_onboarding_contact,
                search_contact_for_onboarding,
                complete_contact_onboarding_flow,
                skip_contact_onboarding_flow,
                add_new_contact,
                update_existing_contact,
                set_reminder,
                find_reminder,
                update_existing_reminder,
                list_all_reminders,
                create_appointment,
                delete_reminder_tool,
                find_appointment,
                select_appointment_for_update,
                apply_appointment_updates,
                handle_appointment_update_flow,
                # find_contact_for_appointment,
                # confirm_contact_for_appointment,
                # add_contact_for_appointment,
                list_all_appointments,
                delete_appointment,
                get_weather,
                morning_greeting,
                validate_zip_code,
            ],
        )
        self.pronunciations = STATIC_PRONUNCIATIONS.copy()
        sorted_terms = sorted(self.pronunciations.keys(), key=len, reverse=True)
        self.compiled_pronunciation_rules = []
        for term in sorted_terms:
            pattern = re.compile(rf"\b{re.escape(term)}\b", re.IGNORECASE)
            ssml_replacement = self.pronunciations[term]
            self.compiled_pronunciation_rules.append((pattern, ssml_replacement))

    async def tts_node(
        self, text_stream: AsyncIterable[str], model_settings: ModelSettings
    ) -> AsyncIterable[rtc.AudioFrame]:
        # This function's internal logic remains unchanged.
        full_text = "".join([chunk async for chunk in text_stream])
        modified_text = full_text
        ssml_used = False

        for pattern_regex, ssml_replacement in self.compiled_pronunciation_rules:
            if pattern_regex.search(modified_text):
                modified_text = pattern_regex.sub(ssml_replacement, modified_text)
                ssml_used = True

        if ssml_used:
            if not modified_text.strip().lower().startswith("<speak>"):
                modified_text = f"<speak>{modified_text}</speak>"
            logger.debug(f"TTS input (SSML processed): {modified_text}")
        else:
            logger.debug(f"TTS input (plain text): {modified_text}")

        async def single_text_chunk_iterable(text: str) -> AsyncIterable[str]:
            yield text

        async for frame in Agent.default.tts_node(
            self, single_text_chunk_iterable(modified_text), model_settings
        ):
            yield frame

    async def on_enter(self):
        logger.info("MyHoudiniAgent session processing on_enter.")
        session = self.session
        user_state = session.userdata.user_state
        contact_state = session.userdata.contact_state

        if not user_state.user_id:
            logger.error(
                "Agent.on_enter: user_id is unexpectedly missing. Agent might not function correctly."
            )
            await session.say(
                "I'm having trouble identifying you right now. Some features might not work.",
                allow_interruptions=False,
            )
            # Depending on severity, you might want to stop the agent or limit functionality.
            # For now, it will proceed but tools requiring user_id will fail.
            return  # Early exit if user_id is critical for even basic interaction

        logger.info(
            f"Agent.on_enter: Attempting to load data for User ID: {user_state.user_id}"
        )
        try:
            user_details_dict = await user_api_service.get_user(user_state.user_id)
            # Process user data from API response
            user_data = None
            if user_details_dict:
                if isinstance(user_details_dict, dict):
                    if "data" in user_details_dict:
                        user_data = user_details_dict["data"]
                    else:
                        # Some API responses might not have a 'data' wrapper
                        user_data = user_details_dict

            if user_data:
                logger.info(
                    f"Agent.on_enter: Existing user data found, updating session state: {user_data}"
                )
                # Ensure onboarding_stage_completed is a list
                if (
                    "onboarding_stage_completed" in user_data
                    and user_data["onboarding_stage_completed"] is not None
                ):
                    if not isinstance(user_data["onboarding_stage_completed"], list):
                        logger.warning(
                            f"onboarding_stage_completed is not a list: {user_data['onboarding_stage_completed']}"
                        )
                        user_data["onboarding_stage_completed"] = (
                            [user_data["onboarding_stage_completed"]]
                            if user_data["onboarding_stage_completed"]
                            else []
                        )
                else:
                    user_data["onboarding_stage_completed"] = []

                # Check if time_zone key is present, if not set default UTC timezone
                if "time_zone" not in user_data or not user_data["time_zone"]:
                    logger.info(
                        "No time_zone found in user data, setting default UTC timezone"
                    )
                    user_data["time_zone"] = "UTC"

                logger.info(
                    f"Processed onboarding_stage_completed: {user_data.get('onboarding_stage_completed')}"
                )
                logger.info(f"Using timezone: {user_data.get('time_zone')}")

                voice_id = user_data.get("voice")

                if voice_id:
                    voice_data = await voice_api_service.get_voice(voice_id)
                    voice_name = (
                        voice_data.get("voice_name") if voice_data else DEFAULT_VOICE
                    )
                    user_data["voice_name"] = voice_name
                else:
                    user_data["voice_name"] = DEFAULT_VOICE

                is_onboarded = user_data.get("is_onboarded")

                if is_onboarded:
                    user_data["is_initial_setup_complete"] = bool(is_onboarded)
                else:
                    user_data["is_initial_setup_complete"] = False

                # Load user data into state
                user_state.load_from_dict(user_data)

                # Update TTS voice
                if (
                    session.tts
                    and hasattr(session.tts, "update_options")
                    and isinstance(session.tts, openai.TTS)
                ):
                    try:
                        voice_to_use = user_state.voice_name or DEFAULT_VOICE
                        session.tts.update_options(voice=voice_to_use)
                        logger.info(
                            f"Agent.on_enter: Initial TTS voice set to: {voice_to_use}"
                        )
                    except Exception as e:
                        logger.error(f"Error setting voice: {e}", exc_info=True)
            else:
                logger.info(
                    f"Agent.on_enter: No existing user data for ID: {user_state.user_id} or 'data' field missing."
                )
                # Initialize defaults for a new user if data is missing
                user_state.voice_name = DEFAULT_VOICE  # Ensure default
                user_state.tts_provider = DEFAULT_PROVIDER
                user_state.onboarding_stage_completed = []

                if (
                    session.tts
                    and hasattr(session.tts, "update_options")
                    and isinstance(session.tts, openai.TTS)
                ):
                    try:
                        session.tts.update_options(voice=user_state.voice_name)
                        logger.info(f"Set default voice to {user_state.voice_name}")
                    except Exception as e:
                        logger.error(f"Error setting default voice: {e}", exc_info=True)

            try:
                contact_relation_list_from_db = (
                    await get_myhoudini_contacts_relationships(user_state.user_id)
                )
                if not contact_relation_list_from_db:
                    logger.warning(
                        f"No contact relations found for user {user_state.user_id}, using empty list"
                    )
                    contact_relation_list_from_db = []
            except Exception as e:
                logger.error(f"Error getting contact relationships: {e}", exc_info=True)
                contact_relation_list_from_db = []

            relations_to_onboard = [
                r
                for r in CONTACT_UPDATE_RELATIONS
                if r not in contact_relation_list_from_db
            ]
            contact_state.relations_to_onboard = CONTACT_UPDATE_RELATIONS
            contact_state.filtered_relations = relations_to_onboard
            contact_state.filtered_contact_relations_for_prompt = ", ".join(
                relations_to_onboard
            )
            logger.info(
                f"Agent.on_enter: Filtered contact relations for prompt: {contact_state.filtered_contact_relations_for_prompt}"
            )

        except Exception as e:
            logger.error(
                f"Agent.on_enter: Failed to load user data/relations for {user_state.user_id}: {e}",
                exc_info=True,
            )
            await session.say(
                "I had some trouble fetching your previous information. We might need to start fresh.",
                allow_interruptions=True,
            )
            contact_relation_list_from_db = []
            contact_state.filtered_relations = CONTACT_UPDATE_RELATIONS
            contact_state.filtered_contact_relations_for_prompt = ", ".join(
                CONTACT_UPDATE_RELATIONS
            )

        # Log the contact relations for debugging
        logger.info(f"Contact relations from DB: {contact_relation_list_from_db}")
        logger.info("Agent.on_enter: Instructions updated.")

        if not user_state.is_initial_setup_complete:
            logger.info(
                "Agent.on_enter: Starting onboarding flow based on completed stages."
            )

            # Determine the next step based on completed stages
            try:
                # Check onboarding status directly from user_state
                logger.info(
                    f"Agent.on_enter: Checking onboarding status: {user_state.onboarding_stage_completed}"
                )

                if not user_state.onboarding_stage_completed:
                    # No stages completed, start with name onboarding
                    onboarding_status = "START_NAME_ONBOARDING"
                elif (
                    "name" in user_state.onboarding_stage_completed
                    and "voice" not in user_state.onboarding_stage_completed
                ):
                    # Name completed, start voice onboarding
                    onboarding_status = "START_VOICE_ONBOARDING"
                elif (
                    "name" in user_state.onboarding_stage_completed
                    and "voice" in user_state.onboarding_stage_completed
                    and "contact" not in user_state.onboarding_stage_completed
                ):
                    # Name and voice completed, start contact onboarding
                    onboarding_status = "START_CONTACT_ONBOARDING"
                else:
                    # All stages completed, mark onboarding as complete
                    onboarding_status = "ONBOARDING_COMPLETE"

                logger.info(f"Agent.on_enter: Onboarding status: {onboarding_status}")

                if onboarding_status == "START_NAME_ONBOARDING":
                    # Start with name onboarding
                    logger.info("Agent.on_enter: Starting name onboarding.")
                    await session.say(
                        START_ONBOARDING_ASSISTANT_DIALOG,
                        allow_interruptions=False,
                    )
                elif onboarding_status == "START_VOICE_ONBOARDING":
                    # Name completed, start voice onboarding
                    logger.info("Agent.on_enter: Starting voice onboarding.")
                    await session.say(
                        "Lets setup the voice, This is my mira voice, but I have some others. Would you like to hear them?",
                        allow_interruptions=True,
                    )
                elif onboarding_status == "START_CONTACT_ONBOARDING":
                    logger.info("Agent.on_enter: Starting contact onboarding.")
                    # Initialize contact state for the first relation if needed
                    if (
                        not contact_state.current_onboarding_relation
                        and contact_state.relations_to_onboard
                    ):
                        contact_state.move_to_next_relation_for_onboarding()  # This sets the first

                    # Check if contacts are synced
                    if session.userdata.user_state.is_synced:
                        # If contacts are synced, start with the first relation

                        all_synced_contacts = (
                            await contact_api_service.get_contacts_by_user(
                                user_state.user_id
                            )
                        )

                        # Initialize onboarding state with synced contacts

                        if len(all_synced_contacts) > 0:
                            contact_state.start_onboarding_relations(
                                # Replace with actual synced contact list if available
                                all_synced_contacts_from_db=all_synced_contacts,
                                relations_to_process=contact_state.filtered_relations,
                            )
                        else:
                            contact_state.start_onboarding_relations(
                                all_synced_contacts_from_db=[],
                                relations_to_process=contact_state.filtered_relations,
                            )

                        if contact_state.filtered_relations:
                            contact_state.move_to_next_relation_for_onboarding()
                            current_relation = contact_state.current_onboarding_relation
                            await session.say(
                                f"Let's add some of your important contacts. Shall we?",
                                allow_interruptions=True,
                            )
                        else:
                            # No relations to onboard
                            await session.say(
                                "Great! We've already set up your key contacts. Would you like to include more, or shall we finish the contact onboarding process?",
                                allow_interruptions=True,
                            )
                    else:
                        # If contacts are not synced, ask to sync
                        await session.say(
                            "Next, to help manage your connections, I can sync with your device's contacts. This helps me find people you mention much faster. Would you like to sync your contacts now? (You can say yes or no)",
                            allow_interruptions=True,
                        )
                else:  # ONBOARDING_COMPLETE or any other status
                    # All stages completed, mark onboarding as complete
                    logger.info(
                        "Agent.on_enter: Onboarding complete, proceeding to general conversation."
                    )
                    await session.say(
                        "We're all set up with the basics! To help me be more useful, would you like to set your time on which you would like to get updated on the day's activities? This can help with scheduling reminders and appointments.",
                        allow_interruptions=True,
                    )
            except Exception as e:
                logger.error(
                    f"Agent.on_enter: Error checking onboarding status: {e}",
                    exc_info=True,
                )
                # Fallback to default onboarding start based on what's already completed
                if (
                    "name" in user_state.onboarding_stage_completed
                    and "voice" in user_state.onboarding_stage_completed
                ):
                    # Name and voice completed, start contact onboarding
                    logger.info("Agent.on_enter: Fallback to contact onboarding.")
                    if session.userdata.user_state.is_synced:
                        # If contacts are synced, start with the first relation
                        if contact_state.filtered_relations:
                            current_relation = contact_state.filtered_relations[0]
                            await session.say(
                                f"Let's add some of your important contacts. First, who is your {current_relation}?",
                                allow_interruptions=True,
                            )
                        else:
                            # No relations to onboard
                            await session.say(
                                "Great! We've already set up your key contacts. Is there anything else you'd like to do?",
                                allow_interruptions=True,
                            )
                    else:
                        # If contacts are not synced, ask to sync
                        await session.say(
                            "Next, to help manage your connections, I can sync with your device's contacts. This helps me find people you mention much faster. Would you like to sync your contacts now? (You can say yes or no)",
                            allow_interruptions=True,
                        )
                elif "name" in user_state.onboarding_stage_completed:
                    # Name completed, start voice onboarding
                    logger.info("Agent.on_enter: Fallback to voice onboarding.")
                    await session.say(
                        "Lets setup the voice, This is my mira voice, but I have some others. Would you like to hear them?",
                        allow_interruptions=True,
                    )
                else:
                    # Start with name onboarding
                    logger.info("Agent.on_enter: Fallback to name onboarding.")
                    await session.say(
                        "Hello, welcome to MyHoudini! I'm your new assistant designed to help you effortlessly manage your contacts, appointments, reminders, and daily schedule. To get started, what's your name?",
                        allow_interruptions=True,
                    )
        elif user_state.is_initial_setup_complete and not user_state.daily_updates_time:
            # check if daily_updates_time is not set
            await session.say(
                "We're all set up with the basics! To help me be more useful, would you like to set your time on which you would like to get updated on the day's activities, Every morning I'll give you an update of \
                    the day's activities at this time. What time would you like me to do this?",
                allow_interruptions=True,
            )
        else:
            logger.info("Agent.on_enter: User setup complete. Agent is ready.")

            current_flow = user_state.current_flow
            current_flow_id = user_state.current_flow_id
            preferred_name = user_state.preferred_name or user_state.name or "there"

            logger.info(f"Agent.on_enter: Current flow: {current_flow}")

            if FlowTrigger.MORNING.value == current_flow:
                logger.info("Agent.on_enter: Triggering morning flow.")
                # --- Trigger Morning Flow ---
                # Save state immediately to prevent re-triggering during this session
                try:
                    # await save_user_info(session.context) # Use session.context here
                    logger.info(
                        "Agent.on_enter: Marked last_morning_flow_date and saved user info."
                    )
                except Exception as e:
                    logger.error(
                        f"Agent.on_enter: Failed to save last_morning_flow_date: {e}",
                        exc_info=True,
                    )
                    # Continue flow, but state might be inconsistent if crash happens now.

                # Say initial greeting for morning flow
                preferred_name = user_state.preferred_name or user_state.name or "there"
                await session.say(
                    f"Good morning, {preferred_name}!, Do you want to hear your today update?",
                    allow_interruptions=True,  # Allow interruption during morning flow
                )

                logger.info("Agent.on_enter: Sent morning greeting.")
            elif FlowTrigger.GENERAL.value == current_flow:
                await session.say(
                    f"Hi {preferred_name}! How can I help you today?",
                    allow_interruptions=True,
                )
            elif FlowTrigger.CONTACT.value == current_flow:
                await session.say(
                    f"Let's add some of your important contacts. Shall we?",
                    allow_interruptions=True,
                )
            elif FlowTrigger.APPOINTMENT.value == current_flow:
                await session.say(
                    f"Want me to help you schedule or check an appointment, {preferred_name}?",
                    allow_interruptions=True,
                )
            elif FlowTrigger.REMINDER.value == current_flow:
                if current_flow_id is not None:
                    logger.info(
                        f"Agent.on_enter: Current flow ID: {current_flow}:{current_flow_id}"
                    )
                    reminder_id = current_flow_id
                    try:
                        reminder = await reminder_api_service.get_reminder_by_id(
                            reminder_id
                        )
                    except Exception as e:
                        logger.error(
                            f"Agent.on_enter: Error fetching reminder: {e}",
                            exc_info=True,
                        )
                        reminder = None
                    if reminder:
                        reminder_title = reminder.get("reminder_text", "")
                        reminder_description = reminder.get("description", "")
                        if reminder["reminder_type"] == "general":
                            # you have tile and description show may be description is missing so make messages accordingly as i have to display and speak
                            if reminder_description:
                                reminder_message = """
                                You have a reminder for: {reminder_title}
                                {reminder_description}
                                """
                            else:
                                reminder_message = (
                                    f"You have a reminder for: {reminder_title}"
                                )
                            await session.say(
                                reminder_message,
                                allow_interruptions=True,
                            )

                        elif reminder["reminder_type"] == "appointment":
                            reminder_title = reminder.get("reminder_text", "")
                            remind_about_participant_from_last_appointment = (
                                reminder.get("participant_reminder", "")
                            )

                            if remind_about_participant_from_last_appointment:
                                appointment_id = reminder["linked_entity"]
                                appointment_details = await appointment_api_service.get_appointments_by_id(
                                    appointment_id
                                )
                                participants_ids_in_appointment = (
                                    appointment_details.get("participant_ids", [])
                                )

                                all_appointments = await appointment_api_service.get_appointments_by_user(
                                    user_state.user_id
                                )
                                sorted_appointments_by_end_time = sorted(
                                    all_appointments,
                                    key=lambda x: x["end_time"],
                                    reverse=True,
                                )

                                last_appointments_summary_for_participants = []

                                for participant_id in participants_ids_in_appointment:
                                    last_appointment = None
                                    participant_info = (
                                        await contact_api_service.get_contact(
                                            participant_id
                                        )
                                    )

                                    for appointment in sorted_appointments_by_end_time:
                                        if (
                                            participant_id
                                            in appointment["participant_ids"]
                                        ):
                                            if last_appointment is None:
                                                last_appointment = appointment
                                                continue

                                            if (
                                                appointment["end_time"]
                                                < last_appointment["end_time"]
                                            ):
                                                last_appointment = appointment
                                                break

                                    if last_appointment:
                                        logger.info("Last appointment found")
                                        try:
                                            start_time = datetime.strptime(
                                                last_appointment["start_time"],
                                                "%Y-%m-%dT%H:%M:%S",
                                            )
                                            end_time = datetime.strptime(
                                                last_appointment["end_time"],
                                                "%Y-%m-%dT%H:%M:%S",
                                            )
                                            date = start_time.strftime("%B %d, %Y")
                                            time_range = f"{start_time.strftime('%I:%M %p')} to {end_time.strftime('%I:%M %p')} on {date}"
                                            summary = f"{time_range} – {last_appointment['summary']}"
                                        except Exception as e:
                                            logger.error(
                                                f"Error parsing appointment time: {e}",
                                                exc_info=True,
                                            )
                                            summary = last_appointment["summary"]

                                        last_appointments_summary_for_participants.append(
                                            {
                                                "participant_name": participant_info[
                                                    "name"
                                                ],
                                                "participant_relationship": participant_info[
                                                    "relationship"
                                                ],
                                                "last_appointment_summary": summary,
                                            }
                                        )

                                # Generate a smooth, spoken summary
                                appointment_lines = []
                                for (
                                    participant
                                ) in last_appointments_summary_for_participants:
                                    # Maintain gap
                                    appointment_lines.append(" ")
                                    # check if relationship is not empty
                                    if participant["participant_relationship"]:
                                        line = (
                                            f"With {participant['participant_name']}, "
                                            f"your {participant['participant_relationship']}. "
                                            f"The last time you met was at {participant['last_appointment_summary']}."
                                        )
                                    else:
                                        line = (
                                            f"With {participant['participant_name']}. "
                                            f"The last time you met was at {participant['last_appointment_summary']}."
                                        )
                                    appointment_lines.append(line)

                                appointment_summary = " ".join(appointment_lines)
                                reminder_message = (
                                    f"You have an upcoming appointment reminder titled '{reminder_title}'. "
                                    f"{reminder_description if reminder_description else ''} "
                                    f"{appointment_summary}"
                                )

                                await session.say(
                                    reminder_message.strip(), allow_interruptions=True
                                )

                            else:
                                if reminder_description:
                                    reminder_message = (
                                        f"You have a reminder titled '{reminder_title}'. "
                                        f"Here are the details: {reminder_description}"
                                    )
                                else:
                                    reminder_message = f"You have a reminder titled '{reminder_title}'."

                                await session.say(
                                    reminder_message.strip(), allow_interruptions=True
                                )

                    else:
                        # Say reminder not found
                        sorry_message = f"Sorry, {preferred_name}, I couldn't find the reminder you're looking for. Can I help you with something else?"
                        await session.say(sorry_message, allow_interruptions=True)
                else:
                    await session.say(
                        f"Want me to help you schedule or check a reminder, {preferred_name}?",
                        allow_interruptions=True,
                    )
            else:
                await session.say(
                    f"Hi {preferred_name}! How can I help you today?",
                    allow_interruptions=True,
                )

        logger.info("Agent.on_enter: Initial message sent.")

        # async def llm_node(
        #     self,
        #     chat_ctx: ChatContext,
        #     tools: list[FunctionTool],
        #     model_settings: ModelSettings,
        # ) -> Union[AsyncIterable[llm.ChatChunk | str], str, llm.ChatChunk, None]:
        #     formatted_system_prompt = chat_assistant.format(
        #         filtered_relation_values=self.session.userdata.filtered_contact_relations_for_prompt
        #     )
        #     current_turn_chat_ctx = ChatContext()
        #     system_message_exists = False
        #     for item in chat_ctx.items:
        #         if item.role == llm.ChatRole.SYSTEM:
        #             current_turn_chat_ctx.messages.append(
        #                 llm.ChatMessage(
        #                     role=llm.ChatRole.SYSTEM, content=formatted_system_prompt
        #                 )
        #             )
        #             system_message_exists = True
        #         else:
        #             current_turn_chat_ctx.messages.append(item)
        #     if not system_message_exists:
        #         current_turn_chat_ctx.messages.insert(
        #             0,
        #             llm.ChatMessage(
        #                 role=llm.ChatRole.SYSTEM, content=formatted_system_prompt
        #             ),
        #         )
        #     current_turn_chat_ctx.truncate(max_items=MAX_CONTEXT_ITEMS)
        #     return await Agent.default.llm_node(
        #         self, current_turn_chat_ctx, tools, model_settings
        #     )

    async def handle_flow_transition(self, new_flow: str):
        """Handle switching flows when metadata updates."""
        logger.info(
            f"Agent.handle_flow_transition: Handling flow change to: {new_flow}"
        )
        session = self.session
        user_state = session.userdata.user_state
        preferred_name = user_state.preferred_name or user_state.name or "there"

        if FlowTrigger.MORNING.value == new_flow:
            await session.say(
                f"Good morning, {preferred_name}!", allow_interruptions=True
            )
        elif FlowTrigger.CONTACT.value == new_flow:
            await session.say(
                f"Let's add some of your important contacts. Shall we?",
                allow_interruptions=True,
            )
        elif FlowTrigger.APPOINTMENT.value == new_flow:
            await session.say(
                f"Want me to help you schedule or check an appointment, {preferred_name}?",
                allow_interruptions=True,
            )
        elif FlowTrigger.REMINDER.value == new_flow:
            await session.say(
                f"Ready to set or check a reminder, {preferred_name}?",
                allow_interruptions=True,
            )
        elif FlowTrigger.GENERAL.value == new_flow:
            await session.say(
                f"Hi {preferred_name}! How can I help you today?",
                allow_interruptions=True,
            )
        else:
            await session.say(
                f"Hi {preferred_name}! I'm ready to help.", allow_interruptions=True
            )


def prewarm_models(proc: JobProcess):
    logger.info("Prewarming VAD model...")
    try:
        proc.userdata["vad"] = silero.VAD.load(
            min_speech_duration=0.2,
            min_silence_duration=0.8,
            activation_threshold=0.7,
        )
        logger.info("VAD model prewarmed successfully.")
    except Exception as e:
        logger.error(f"Failed to prewarm VAD model: {e}", exc_info=True)
    # ... (other prewarming for TTS, STT, NC as before) ...
    try:
        logger.info("Attempting to prewarm TTS (OpenAI)...")
        _ = openai.TTS(api_key=OPENAI_API_KEY, model="tts-1", voice="alloy")
        logger.info("OpenAI TTS likely initialized.")
    except Exception as e:
        logger.warning(f"Could not prewarm OpenAI TTS: {e}")
    try:
        logger.info("Attempting to prewarm STT (Deepgram)...")
        _ = deepgram.STT(
            api_key=DEEPGRAM_API_KEY, model="nova-2-general", language="en-US"
        )
        logger.info("Deepgram STT likely initialized.")
    except Exception as e:
        logger.warning(f"Could not prewarm Deepgram STT: {e}")
    try:
        logger.info("Attempting to prewarm Noise Cancellation (BVC)...")
        _ = noise_cancellation.BVC()
        logger.info("BVC Noise Cancellation initialized.")
    except Exception as e:
        logger.warning(f"Could not prewarm BVC Noise Cancellation: {e}")


# Removed use_test_id flag
def get_user_id_from_room(ctx: JobContext) -> Optional[str]:
    logger.info(
        "Attempting to determine user_id from currently connected participants..."
    )
    for _sid, participant in ctx.room.remote_participants.items():
        if participant.kind != rtc.ParticipantKind.PARTICIPANT_KIND_AGENT:
            user_identity = participant.identity
            logger.info(
                f"Found existing non-agent participant: {user_identity}. Using this as user_id."
            )
            return user_identity


def get_user_metadata_from_room(
    ctx: JobContext,
) -> Optional[str]:  # Removed use_test_id flag
    logger.info(
        "Attempting to determine user_metadata from currently connected participants..."
    )
    for _sid, participant in ctx.room.remote_participants.items():
        if participant.kind != rtc.ParticipantKind.PARTICIPANT_KIND_AGENT:
            user_metadata = participant.metadata
            logger.info(
                f"Found existing non-agent participant: {participant}. Using this as user_metadata."
            )
            return user_metadata

    # If no non-agent remote participant is initially found, return None.
    # The entrypoint will then wait for a new participant connection if this is None.
    logger.info("No non-agent remote participant found initially.")
    return None


async def entrypoint(ctx: JobContext):
    logger.info(f"Entrypoint started for room: {ctx.room.name}, Job ID: {ctx.job.id}")

    # --- 1. Connect the Agent to the Room IMMEDIATELY ---
    try:
        logger.info(
            f"Entrypoint: Attempting to connect agent to room '{ctx.room.name}'..."
        )
        await ctx.connect(auto_subscribe=AutoSubscribe.AUDIO_ONLY)
        logger.info(
            f"Entrypoint: Agent successfully connected to room '{ctx.room.name}'."
        )
    except Exception as e:
        logger.error(
            f"Entrypoint: CRITICAL - Failed to connect agent to room: {e}",
            exc_info=True,
        )
        return

    # --- 2. Determine User ID ---
    user_id_for_session = get_user_id_from_room(ctx)
    logger.info(
        f"Entrypoint: Determined initial user_id for session (from existing participants): {user_id_for_session}"
    )

    # --- 3. Initialize SessionData ---
    initial_session_data = SessionData(room=ctx.room, user_id=user_id_for_session)
    logger.info(
        f"Entrypoint: SessionData initialized. User ID: {initial_session_data.user_state.user_id}"
    )

    # Attempt to fetch initial event_type from metadata
    user_metadata_for_session = get_user_metadata_from_room(ctx)
    actual_user_metadata_str = user_metadata_for_session or "{}"
    metadata = json.loads(actual_user_metadata_str)
    initial_session_data.user_state.current_flow = metadata.get("event_type")
    initial_session_data.user_state.current_flow_id = metadata.get("event_id", None)

    # --- 4. Initialize Plugins ---
    try:
        stt_plugin = create_resilient_stt(
            api_key=DEEPGRAM_API_KEY, model="nova-3", language="en-US"
        )

        llm_plugin = google.LLM(model=GEMINI_MODEL_LLM, vertexai=True, project=GOOGLE_CLOUD_PROJECT, location=GOOGLE_CLOUD_LOCATION)
        tts_plugin = openai.TTS(
            model="tts-1",
            voice=initial_session_data.user_state.voice_name or DEFAULT_VOICE,
            api_key=OPENAI_API_KEY,
        )
        vad_plugin = silero.VAD.load(
            min_speech_duration=0.2,
            min_silence_duration=0.8,
            activation_threshold=0.7,
        )
        nc_plugin = noise_cancellation.BVC()
        turn_detector = MultilingualModel()
        logger.info("Entrypoint: Plugins initialized.")
    except Exception as e:
        logger.error(
            f"Entrypoint: Failed to initialize one or more plugins: {e}", exc_info=True
        )
        if ctx.room:
            await ctx.room.disconnect()
        return

    # --- 5. Initialize AgentSession ---
    agent_session = AgentSession(
        stt=stt_plugin,
        llm=llm_plugin,
        tts=tts_plugin,
        vad=vad_plugin,
        # turn_detection=turn_detector,
        userdata=initial_session_data,
        allow_interruptions=True,
        min_interruption_duration=0.8,
    )
    logger.info("Entrypoint: AgentSession initialized.")

    # --- 6. Participant Connection Handling ---
    if not initial_session_data.user_state.user_id:
        logger.info("Entrypoint: No user ID yet. Waiting for non-agent participant...")

        participant_connected_event = asyncio.Event()

        @ctx.room.on("participant_connected")
        def _on_participant_connected(participant: rtc.RemoteParticipant, *_args):
            if (
                not participant_connected_event.is_set()
                and participant.kind != rtc.ParticipantKind.PARTICIPANT_KIND_AGENT
            ):
                identity = participant.identity
                metadata = json.loads(participant.metadata or "{}")
                logger.info(
                    f"Entrypoint: Non-agent participant '{identity}' connected. Assigning session user ID."
                )
                initial_session_data.user_state.user_id = identity
                initial_session_data.user_state.current_flow = metadata.get(
                    "event_type"
                )
                initial_session_data.user_state.current_flow_id = metadata.get(
                    "event_id", None
                )

                if (
                    initial_session_data.appointment_state
                    and not initial_session_data.appointment_state.user_id
                ):
                    initial_session_data.appointment_state.user_id = identity
                if (
                    initial_session_data.reminder_state
                    and not initial_session_data.reminder_state.user_id
                ):
                    initial_session_data.reminder_state.user_id = identity
                participant_connected_event.set()

        try:
            await asyncio.wait_for(participant_connected_event.wait(), timeout=60.0)
        except asyncio.TimeoutError:
            logger.error("Entrypoint: Timeout waiting for participant. Exiting.")
            try:
                async for _ in tts_plugin.synthesize(
                    "I'm sorry, I couldn't identify a user in this room. Please try rejoining."
                ):
                    pass
            except Exception as direct_say_err:
                logger.error(
                    f"Entrypoint: Error synthesizing timeout message: {direct_say_err}"
                )
            if ctx.room:
                await ctx.room.disconnect()
            return

    # --- 7. Metadata Change Listener ---
    @ctx.room.on("participant_metadata_changed")
    def _on_metadata_change(participant: rtc.RemoteParticipant):
        def sync_handler():
            try:
                if participant.identity == initial_session_data.user_state.user_id:
                    metadata = json.loads(participant.metadata or "{}")
                    new_event_type = metadata.get("event_type")
                    if new_event_type:
                        logger.info(
                            f"Metadata changed: Updating flow to '{new_event_type}'"
                        )
                        initial_session_data.user_state.current_flow = new_event_type
                        initial_session_data.user_state.current_flow_id = metadata.get(
                            "event_id", None
                        )
                        asyncio.create_task(
                            agent.handle_flow_transition(new_event_type)
                        )
            except Exception as e:
                logger.error(f"Error in metadata change handler: {e}", exc_info=True)

        sync_handler()

    # --- 8. Final Check before Launching Agent ---
    if not initial_session_data.user_state.user_id:
        logger.critical(
            "Entrypoint: User ID missing before agent start. Disconnecting..."
        )
        if ctx.room:
            await ctx.room.disconnect()
        return

    # --- 9. Start Agent Session ---
    agent = MyHoudiniAgent()
    logger.info("Entrypoint: Launching AgentSession...")
    try:
        await agent_session.start(
            agent=agent,
            room=ctx.room,
            room_input_options=RoomInputOptions(
                audio_enabled=True, video_enabled=False
            ),
            room_output_options=RoomOutputOptions(
                audio_enabled=True, transcription_enabled=True
            ),
        )
        logger.info("Entrypoint: AgentSession started successfully.")
    except Exception as e:
        logger.error(f"Entrypoint: Failed to start AgentSession: {e}", exc_info=True)
        if ctx.room:
            await ctx.room.disconnect()


# ... (request_fnc and if __name__ == "__main__" block remain the same) ...
# Ensure these are identical to your previous correct version
async def request_fnc(req: JobRequest):
    logger.info(
        f"Received job request: {req.job.id} for room {req.room.name} (SID: {req.room.sid})"
    )
    identity = "houdini-assistant-agent"
    await req.accept(identity=identity, name="MyHoudini Assistant")
    logger.info(f"Accepted job request {req.job.id} with agent identity '{identity}'")


if __name__ == "__main__":
    from dotenv import load_dotenv

    load_dotenv()
    if not OPENAI_API_KEY:
        logger.error("OPENAI_API_KEY not found.")
    if not DEEPGRAM_API_KEY:
        logger.error("DEEPGRAM_API_KEY not found.")

    worker_options = WorkerOptions(
        entrypoint_fnc=entrypoint,
        request_fnc=request_fnc,
        worker_type=WorkerType.ROOM,
        prewarm_fnc=prewarm_models,
    )
    cli.run_app(worker_options)
