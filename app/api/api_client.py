"""
API client for the MyHoudini API server.

This module provides a client for interacting with the MyHoudini API server using the requests library.
"""

import json
import logging
import os
from datetime import datetime
from typing import Any, Dict, List, Optional, Union

import aiohttp
from bson import ObjectId

from app.constants.env_constants import API_ENDPOINT_KEY, API_SERVER_ENDPOINT

logger = logging.getLogger(__name__)


class APIClient:
    """Client for the MyHoudini API server."""

    def __init__(self, base_url: str = API_SERVER_ENDPOINT):
        """
        Initialize the API client.

        Args:
            base_url: The base URL of the API server
        """
        self.base_url = base_url.rstrip("/")
        logger.info(f"Initialized API client with base URL: {self.base_url}")

    async def _request(
        self,
        method: str,
        endpoint: str,
        data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None,
        auth_key: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Make a request to the API server.

        Args:
            method: The HTTP method to use
            endpoint: The API endpoint to call
            data: Optional data to send in the request body
            params: Optional query parameters

        Returns:
            The response from the API server

        Raises:
            Exception: If the request fails
        """
        url = f"{self.base_url}{endpoint}"
        print("\n\n\n\nrequest url:", url, "\n\n", endpoint)

        # Convert ObjectId to string in both data and params
        if data:
            data = self._convert_objectid_to_str(data)
        if params:
            params = self._convert_objectid_to_str(params)

        try:
            logger.debug(f"Making {method} request to {url}")
            if data:
                logger.debug(f"Request data: {data}")
            if params:
                logger.debug(f"Request params: {params}")

            headers = {"Authorization": auth_key or API_ENDPOINT_KEY}
            async with aiohttp.ClientSession() as session:
                async with session.request(
                    method=method, url=url, json=data, params=params, headers=headers
                ) as response:
                    if response.status >= 400:
                        error_text = await response.text()
                        logger.error(
                            f"API request error: {response.status}, message='{error_text}', url='{url}'"
                        )
                        raise Exception(
                            f"API request error: {response.status}, message='{error_text}'"
                        )

                    try:
                        result = await response.json()
                        logger.info(f"API response: {result}")
                        # Check if the result is a dictionary with a 'data' field
                        if isinstance(result, dict) and "data" in result:
                            return result["data"]
                        return result
                    except json.JSONDecodeError:
                        text = await response.text()
                        logger.error(f"Failed to parse API response: {text}")
                        raise Exception(f"Failed to parse API response: {text}")

        except aiohttp.ClientError as e:
            logger.error(f"API request error: {str(e)}")
            raise Exception(f"API request error: {str(e)}")

    def _convert_objectid_to_str(self, data: Any) -> Any:
        """
        Convert ObjectId values to strings, boolean values to string 'true'/'false',
        and numeric values to strings in any data structure.

        Args:
            data: The data to convert (dict, list, ObjectId, bool, int, float, or other)

        Returns:
            The converted data
        """
        if isinstance(data, ObjectId):
            return str(data)
        elif isinstance(data, bool):
            return "true" if data else "false"
        elif isinstance(data, dict):
            result = {}
            for key, value in data.items():
                result[key] = self._convert_objectid_to_str(value)
            return result
        elif isinstance(data, list):
            return [self._convert_objectid_to_str(item) for item in data]
        elif isinstance(data, (int, float)):
            return str(data)
        else:
            return data

    # User endpoints

    async def create_user(self, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a new user.

        Args:
            user_data: The user data

        Returns:
            The created user
        """
        try:
            logger.debug(f"Creating user with data: {user_data}")
            # The _request method will handle ObjectId conversion
            result = await self._request("POST", "/api/users/new", data=user_data)
            logger.debug(f"User created successfully: {result}")
            return result
        except Exception as e:
            logger.error(f"Error creating user: {str(e)}")
            raise

    async def get_user(self, user_id: Union[str, ObjectId]) -> Dict[str, Any]:
        """
        Get a user by ID.

        Args:
            user_id: The ID of the user to get

        Returns:
            The user with the specified ID
        """
        user_id_str = str(user_id) if isinstance(user_id, ObjectId) else user_id
        endpoint = f"/api/users/{user_id_str}"
        print("\n\n\n\nget_user endpoint:", endpoint, "\n\n\n\n")
        return await self._request("GET", endpoint)

    async def get_user_by_participant_id(self, participant_id: str) -> Dict[str, Any]:
        """
        Get a user by participant ID.

        Args:
            participant_id: The participant ID of the user to get

        Returns:
            The user with the specified participant ID
        """
        return await self._request("GET", f"/api/users/by-participant/{participant_id}")

    # async def get_user_by_voice_id(
    #     self, voice_id: Union[str, ObjectId]
    # ) -> Dict[str, Any]:
    #     """
    #     Get a user by voice ID.

    #     Args:
    #         voice_id: The voice ID of the user to get

    #     Returns:
    #         The user with the specified voice ID
    #     """
    #     voice_id_str = str(voice_id) if isinstance(voice_id, ObjectId) else voice_id
    #     return await self._request("GET", f"/api/users/by-voice/{voice_id_str}")

    async def get_users(self, skip: int = 0, limit: int = 100) -> List[Dict[str, Any]]:
        """
        Get all users.

        Args:
            skip: Number of users to skip
            limit: Maximum number of users to return

        Returns:
            A list of users
        """
        params = {"skip": skip, "limit": limit}
        return await self._request("GET", "/api/users", params=params)

    async def update_user(
        self, user_id: Union[str, ObjectId], user_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Update a user by ID.

        Args:
            user_id: The ID of the user to update
            user_data: The user data to update

        Returns:
            The updated user
        """
        try:
            logger.debug(f"Updating user with ID: {user_id}, data: {user_data}")
            # Convert user_id to string if it's an ObjectId
            user_id_str = str(user_id) if isinstance(user_id, ObjectId) else user_id
            # The _request method will handle ObjectId conversion in user_data
            result = await self._request(
                "PUT", f"/api/users/{user_id_str}", data=user_data
            )
            logger.info(f"User updated successfully: {result}")
            return result
        except Exception as e:
            logger.error(f"Error updating user: {str(e)}", exc_info=True)
            raise

    async def delete_user(self, user_id: Union[str, ObjectId]) -> Dict[str, Any]:
        """
        Delete a user by ID.

        Args:
            user_id: The ID of the user to delete

        Returns:
            The deleted user
        """
        user_id_str = str(user_id) if isinstance(user_id, ObjectId) else user_id
        return await self._request("DELETE", f"/api/users/{user_id_str}")

    # Contact endpoints

    async def search_contacts_by_name(self, user_id: str, name: str) -> list[dict]:
        """
        Search for contacts by user_id and name (case-insensitive, matches anywhere in name).

        Args:
            user_id: The ID of the user whose contacts to search
            name: The name (or part of name) to search for

        Returns:
            A list of matching contacts

        Raises:
            Exception: If the API request fails
        """
        try:
            endpoint = f"/api/contacts/search/{user_id}"
            params = {"name": name}
            logger.debug(f"Searching contacts for user_id={user_id} with name={name}")
            result = await self._request("GET", endpoint, params=params)
            logger.debug(f"Search contacts result: {result}")
            return result
        except Exception as e:
            logger.error(f"Error searching contacts: {str(e)}")
            raise

    async def create_contact(self, contact_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a new contact.

        Args:
            contact_data: The contact data

        Returns:
            The created contact
        """
        try:
            logger.debug(f"Creating contact with data: {contact_data}")
            # The _request method will handle ObjectId conversion
            result = await self._request("POST", "/api/contacts", data=contact_data)
            logger.debug(f"Contact created successfully: {result}")
            return result
        except Exception as e:
            logger.error(f"Error creating contact: {str(e)}")
            raise

    async def get_contact(self, contact_id: Union[str, ObjectId]) -> Dict[str, Any]:
        """
        Get a contact by ID.

        Args:
            contact_id: The ID of the contact to get

        Returns:
            The contact with the specified ID
        """
        contact_id_str = (
            str(contact_id) if isinstance(contact_id, ObjectId) else contact_id
        )
        return await self._request("GET", f"/api/contacts/{contact_id_str}")

    async def get_contacts_by_user(
        self, user_id: Union[str, ObjectId]
    ) -> List[Dict[str, Any]]:
        """
        Get contacts by user ID.

        Args:
            user_id: The ID of the user

        Returns:
            A list of contacts for the specified user
        """
        user_id_str = str(user_id) if isinstance(user_id, ObjectId) else user_id
        return await self._request("GET", f"/api/contacts/user/{user_id_str}")

    async def get_houdini_contacts(
        self,
        user_id: Union[str, ObjectId],
        relation: Optional[str] = None,
    ) -> List[Dict[str, Any]]:
        """
        Get contacts by user ID where is_myhoudini is True.

        Args:
            user_id: The ID of the user
            relation: Optional relation type to filter by

        Returns:
            A list of contacts for the specified user with is_myhoudini=True
        """
        user_id_str = str(user_id) if isinstance(user_id, ObjectId) else user_id
        params = {}
        if relation:
            params["relation"] = relation
        return await self._request(
            "GET", f"/api/contacts/myhoudini/{user_id_str}", params=params
        )

    async def get_contacts(
        self, skip: int = 0, limit: int = 100
    ) -> List[Dict[str, Any]]:
        """
        Get all contacts.

        Args:
            skip: Number of contacts to skip
            limit: Maximum number of contacts to return

        Returns:
            A list of contacts
        """
        params = {"skip": skip, "limit": limit}
        return await self._request("GET", "/api/contacts", params=params)

    async def update_contact(
        self, contact_id: Union[str, ObjectId], contact_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Update a contact by ID.

        Args:
            contact_id: The ID of the contact to update
            contact_data: The contact data to update

        Returns:
            The updated contact
        """
        try:
            logger.debug(
                f"Updating contact with ID: {contact_id}, data: {contact_data}"
            )
            # Convert contact_id to string if it's an ObjectId
            contact_id_str = (
                str(contact_id) if isinstance(contact_id, ObjectId) else contact_id
            )
            # The _request method will handle ObjectId conversion in contact_data
            result = await self._request(
                "PUT", f"/api/contacts/{contact_id_str}", data=contact_data
            )
            logger.debug(f"Contact updated successfully: {result}")
            return result
        except Exception as e:
            logger.error(f"Error updating contact: {str(e)}")
            raise

    async def delete_contact(self, contact_id: Union[str, ObjectId]) -> Dict[str, Any]:
        """
        Delete a contact by ID.

        Args:
            contact_id: The ID of the contact to delete

        Returns:
            The deleted contact
        """
        contact_id_str = (
            str(contact_id) if isinstance(contact_id, ObjectId) else contact_id
        )
        return await self._request("DELETE", f"/api/contacts/{contact_id_str}")

    # Voice endpoints

    async def get_voice(self, voice_id: Union[str, ObjectId]) -> Dict[str, Any]:
        """
        Get a voice by ID.

        Args:
            voice_id: The ID of the voice to get

        Returns:
            The voice with the specified ID
        """
        voice_id_str = str(voice_id) if isinstance(voice_id, ObjectId) else voice_id
        return await self._request("GET", f"/api/voices/{voice_id_str}")

    async def get_voice_by_name(
        self, voice_name: str, provider: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Get a voice by name.

        Args:
            voice_name: The name of the voice to get
            provider: Optional provider to filter by

        Returns:
            The voice with the specified name
        """
        params = {}
        if provider:
            params["provider"] = provider
        return await self._request(
            "GET", f"/api/voices/by-name/{voice_name}", params=params
        )

    async def get_voices(
        self,
        provider: Optional[str] = None,
        gender: Optional[str] = None,
        language: Optional[str] = None,
        skip: int = 0,
        limit: int = 100,
    ) -> List[Dict[str, Any]]:
        """
        Get all voices.

        Args:
            provider: Optional provider to filter by
            gender: Optional gender to filter by
            language: Optional language to filter by
            skip: Number of voices to skip
            limit: Maximum number of voices to return

        Returns:
            A list of voices
        """
        params = {"skip": skip, "limit": limit}
        if provider:
            params["provider"] = provider
        if gender:
            params["gender"] = gender
        if language:
            params["language"] = language
        return await self._request("GET", "/api/voices", params=params)

    # Reminder endpoints

    async def create_reminder(self, reminder_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a new reminder.

        Args:
            reminder_data: The reminder data

        Returns:
            The created reminder
        """
        try:
            logger.debug(f"Creating reminder with data: {reminder_data}")
            result = await self._request("POST", "/api/reminders", data=reminder_data)
            logger.debug(f"Reminder created successfully: {result}")
            return result
        except Exception as e:
            logger.error(f"Error creating reminder: {str(e)}")
            raise

    async def get_reminder(self, reminder_id: Union[str, ObjectId]) -> Dict[str, Any]:
        """
        Get a reminder by ID.

        Args:
            reminder_id: The ID of the reminder to get

        Returns:
            The reminder with the specified ID
        """
        reminder_id_str = (
            str(reminder_id) if isinstance(reminder_id, ObjectId) else reminder_id
        )
        return await self._request("GET", f"/api/reminders/{reminder_id_str}")

    async def get_reminders_by_user(
        self, user_id: Union[str, ObjectId], active_only: bool = True
    ) -> List[Dict[str, Any]]:
        """
        Get reminders by user ID.

        Args:
            user_id: The ID of the user
            active_only: If True, only return active reminders

        Returns:
            A list of reminders for the specified user
        """
        try:
            user_id_str = str(user_id) if isinstance(user_id, ObjectId) else user_id
            params = {}
            if active_only is not None:
                # Pass boolean directly, _convert_objectid_to_str will handle conversion
                params["is_active"] = active_only

            logger.debug(
                f"Getting reminders for user {user_id_str} with params: {params}"
            )

            response = await self._request(
                "GET", f"/api/reminders/user/{user_id_str}", params=params
            )

            # Handle different response formats
            if isinstance(response, dict) and "data" in response:
                return response["data"]
            return response
        except Exception as e:
            logger.error(f"Error getting reminders for user: {str(e)}")
            return []

    async def find_with_reminder_text(
        self,
        user_id: Union[str, ObjectId],
        reminder_text: str,
        active_only: bool = True,
    ) -> List[Dict[str, Any]]:
        """
        Search for reminders containing specific text for a user.

        Args:
            user_id: The ID of the user
            reminder_text: Text to search for in reminders
            active_only: If True, only return active reminders

        Returns:
            A list of reminders for the specified user
        """
        try:
            user_id_str = str(user_id) if isinstance(user_id, ObjectId) else user_id
            params = {}
            if reminder_text:
                params["reminder_text"] = reminder_text
            # Pass boolean directly, _convert_objectid_to_str will handle conversion
            if active_only is not None:
                params["is_active"] = active_only

            logger.debug(f"Searching reminders with params: {params}")

            response = await self._request(
                "GET", f"/api/reminders/search/{user_id_str}", params=params
            )

            # Handle different response formats
            if isinstance(response, dict) and "data" in response:
                return response["data"]
            return response
        except Exception as e:
            logger.error(f"Error searching reminders with text: {str(e)}")
            return []

    async def get_reminders(
        self, skip: int = 0, limit: int = 100, active_only: bool = None
    ) -> List[Dict[str, Any]]:
        """
        Get all reminders.

        Args:
            skip: Number of reminders to skip
            limit: Maximum number of reminders to return
            active_only: If provided, filter by active status

        Returns:
            A list of reminders
        """
        try:
            params = {
                "skip": skip,
                "limit": limit,
            }  # _convert_objectid_to_str will handle conversion
            if active_only is not None:
                params["is_active"] = (
                    active_only  # _convert_objectid_to_str will handle conversion
                )

            logger.debug(f"Getting all reminders with params: {params}")

            response = await self._request("GET", "/api/reminders", params=params)

            # Handle different response formats
            if isinstance(response, dict) and "data" in response:
                return response["data"]
            return response
        except Exception as e:
            logger.error(f"Error getting all reminders: {str(e)}")
            return []

    async def update_reminder(
        self, reminder_id: Union[str, ObjectId], reminder_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Update a reminder by ID.

        Args:
            reminder_id: The ID of the reminder to update
            reminder_data: The reminder data to update

        Returns:
            The updated reminder
        """
        try:
            logger.debug(
                f"Updating reminder with ID: {reminder_id}, data: {reminder_data}"
            )
            reminder_id_str = (
                str(reminder_id) if isinstance(reminder_id, ObjectId) else reminder_id
            )
            result = await self._request(
                "PUT", f"/api/reminders/{reminder_id_str}", data=reminder_data
            )
            logger.debug(f"Reminder updated successfully: {result}")
            return result
        except Exception as e:
            logger.error(f"Error updating reminder: {str(e)}")
            raise

    async def delete_reminder(
        self, reminder_id: Union[str, ObjectId]
    ) -> Dict[str, Any]:
        """
        Delete a reminder by ID.

        Args:
            reminder_id: The ID of the reminder to delete

        Returns:
            The deleted reminder
        """
        reminder_id_str = (
            str(reminder_id) if isinstance(reminder_id, ObjectId) else reminder_id
        )
        return await self._request("DELETE", f"/api/reminders/{reminder_id_str}")

    async def update_reminder_status(
        self, reminder_id: Union[str, ObjectId], is_active: bool
    ) -> Dict[str, Any]:
        """
        Update the active status of a reminder.

        Args:
            reminder_id: The ID of the reminder to update
            is_active: The new active status

        Returns:
            The updated reminder
        """
        try:
            logger.debug(
                f"Updating reminder status with ID: {reminder_id}, is_active: {is_active}"
            )
            reminder_id_str = (
                str(reminder_id) if isinstance(reminder_id, ObjectId) else reminder_id
            )
            result = await self._request(
                "PATCH",
                f"/api/reminders/{reminder_id_str}/status",
                data={"is_active": is_active},
            )
            logger.debug(f"Reminder status updated successfully: {result}")
            return result
        except Exception as e:
            logger.error(f"Error updating reminder status: {str(e)}")
            raise

    # Appointment endpoints

    async def create_appointment(
        self, appointment_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Create a new appointment.

        Args:
            appointment_data: The appointment data

        Returns:
            The created appointment
        """
        try:
            logger.debug(f"Creating appointment with data: {appointment_data}")
            result = await self._request(
                "POST", "/api/appointments", data=appointment_data
            )
            logger.debug(f"Appointment created successfully: {result}")
            return result
        except Exception as e:
            logger.error(f"Error creating appointment: {str(e)}")
            raise

    async def get_appointment(
        self, appointment_id: Union[str, ObjectId]
    ) -> Dict[str, Any]:
        """
        Get an appointment by ID.

        Args:
            appointment_id: The ID of the appointment to get

        Returns:
            The appointment with the specified ID
        """
        appointment_id_str = (
            str(appointment_id)
            if isinstance(appointment_id, ObjectId)
            else appointment_id
        )
        return await self._request("GET", f"/api/appointments/{appointment_id_str}")

    async def get_appointments_by_user(
        self, user_id: Union[str, ObjectId], active_only: bool = True
    ) -> List[Dict[str, Any]]:
        """
        Get appointments by user ID.

        Args:
            user_id: The ID of the user
            active_only: If True, only return active appointments

        Returns:
            A list of appointments for the specified user
        """
        try:
            user_id_str = str(user_id) if isinstance(user_id, ObjectId) else user_id
            params = {}
            if active_only is not None:
                params["is_active"] = (
                    active_only  # _convert_objectid_to_str will handle conversion
                )

            logger.debug(
                f"Getting appointments for user {user_id_str} with params: {params}"
            )

            response = await self._request(
                "GET", f"/api/appointments/user/{user_id_str}", params=params
            )

            # Handle different response formats
            if isinstance(response, dict) and "data" in response:
                return response["data"]
            return response
        except Exception as e:
            logger.error(f"Error getting appointments for user: {str(e)}")
            return []

    async def get_appointments_with_linked_entity(
        self, user_id: Union[str, ObjectId], linked_entity: Union[str, ObjectId]
    ) -> List[Dict[str, Any]]:
        """
        GET /api/reminders/by-linked-entity/?user_id=64f1a2b3c4d5e6f7890abcde&linked_entity=60e2b4c5d6e7f8a9012bcdef
        Get appointments by user ID.

        Args:
            user_id: The ID of the user
            linked_entity: The ID of the linked entity
            active_only: If True, only return active appointments

        Returns:
            A list of appointments for the specified user
        """
        user_id_str = str(user_id) if isinstance(user_id, ObjectId) else user_id
        linked_entity_str = (
            str(linked_entity) if isinstance(linked_entity, ObjectId) else linked_entity
        )

        return await self._request(
            "GET",
            f"/api/reminders/by-linked-entity/?user_id={user_id_str}&linked_entity={linked_entity_str}",
        )

    async def get_appointments(
        self, skip: int = 0, limit: int = 100, active_only: bool = None
    ) -> List[Dict[str, Any]]:
        """
        Get all appointments.

        Args:
            skip: Number of appointments to skip
            limit: Maximum number of appointments to return
            active_only: If provided, filter by active status

        Returns:
            A list of appointments
        """
        try:
            params = {
                "skip": skip,
                "limit": limit,
            }  # _convert_objectid_to_str will handle conversion
            if active_only is not None:
                params["is_active"] = (
                    active_only  # _convert_objectid_to_str will handle conversion
                )

            logger.debug(f"Getting all appointments with params: {params}")

            response = await self._request("GET", "/api/appointments", params=params)

            # Handle different response formats
            if isinstance(response, dict) and "data" in response:
                return response["data"]
            return response
        except Exception as e:
            logger.error(f"Error getting all appointments: {str(e)}")
            return []

    async def update_appointment(
        self, appointment_id: Union[str, ObjectId], appointment_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Update an appointment by ID.

        Args:
            appointment_id: The ID of the appointment to update
            appointment_data: The appointment data to update

        Returns:
            The updated appointment
        """
        try:
            logger.debug(
                f"Updating appointment with ID: {appointment_id}, data: {appointment_data}"
            )
            appointment_id_str = (
                str(appointment_id)
                if isinstance(appointment_id, ObjectId)
                else appointment_id
            )
            result = await self._request(
                "PUT", f"/api/appointments/{appointment_id_str}", data=appointment_data
            )
            logger.debug(f"Appointment updated successfully: {result}")
            return result
        except Exception as e:
            logger.error(f"Error updating appointment: {str(e)}")
            raise

    async def delete_appointment(
        self, appointment_id: Union[str, ObjectId]
    ) -> Dict[str, Any]:
        """
        Delete an appointment by ID.

        Args:
            appointment_id: The ID of the appointment to delete

        Returns:
            The deleted appointment
        """
        appointment_id_str = (
            str(appointment_id)
            if isinstance(appointment_id, ObjectId)
            else appointment_id
        )
        return await self._request("DELETE", f"/api/appointments/{appointment_id_str}")

    # Token endpoints

    async def get_livekit_token(self, room_name: str, participant_name: str) -> str:
        """
        Generate a LiveKit token.

        Args:
            room_name: The name of the room to join
            participant_name: The identity of the participant

        Returns:
            The generated token
        """
        params = {"room_name": room_name, "participant_name": participant_name}
        response = await self._request("GET", "/api/tokens/livekit", params=params)
        return response["token"]
    
    async def get_user_weather(self, user_id: Union[str, ObjectId]) -> Dict[str, Any]:
        """
        Get weather information for a user based on their location.

        Args:
            user_id: The ID of the user

        Returns:
            A dictionary containing the weather data
        """
        try:
            user_id_str = str(user_id) if isinstance(user_id, ObjectId) else user_id
            endpoint = f"/api/users/{user_id_str}/weather"
            logger.debug(f"Fetching weather data from endpoint: {endpoint}")
            result = await self._request("GET", endpoint)
            logger.debug(f"Weather data fetched successfully: {result}")
            return result
        except Exception as e:
            logger.error(f"Error fetching weather data for user {user_id}: {str(e)}")
            raise



# Create a singleton instance
api_client = APIClient()
