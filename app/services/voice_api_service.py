"""
Voice API service for interacting with the API server.

This module provides functions for interacting with the voice-related endpoints of the API server.
"""

import logging
from typing import Any, Dict, List, Optional, Union

from bson import ObjectId
from app.api.api_client import api_client

logger = logging.getLogger(__name__)


async def create_voice(voice_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Create a new voice via the API.

    Args:
        voice_data: Dictionary containing voice attributes

    Returns:
        The created voice dictionary

    Raises:
        Exception if the request fails
    """
    try:
        logger.info(f"Creating voice with data: {voice_data}")
        voice = await api_client.create_voice(voice_data)
        logger.info(f"Voice created with ID: {voice.get('_id')}")
        return voice
    except Exception as e:
        logger.error(f"Error creating voice: {str(e)}")
        raise


async def get_voice(voice_id: Union[str, ObjectId]) -> Dict[str, Any]:
    """
    Get a voice by ID.

    Args:
        voice_id: The ID of the voice to retrieve

    Returns:
        The voice dictionary or empty dict on failure
    """
    try:
        logger.info(f"Fetching voice with ID: {voice_id}")
        voice = await api_client.get_voice(voice_id)
        if voice and isinstance(voice, dict):
            return voice
        logger.warning(f"Unexpected response for voice {voice_id}: {voice}")
        return {}
    except Exception as e:
        logger.error(f"Error fetching voice: {str(e)}")
        return {}


async def get_voice_by_name(voice_name: str, provider: Optional[str] = None) -> Dict[str, Any]:
    """
    Get a voice by name and optional provider.

    Args:
        voice_name: The name of the voice
        provider: The voice provider (optional)

    Returns:
        A dictionary containing the voice data
    """
    try:
        logger.info(f"Fetching voice by name: {voice_name}, provider: {provider}")
        voice = await api_client.get_voice_by_name(voice_name, provider)
        return voice
    except Exception as e:
        logger.error(f"Error fetching voice by name: {str(e)}")
        return {}


async def get_all_voices(
    provider: Optional[str] = None,
    gender: Optional[str] = None,
    language: Optional[str] = None,
    skip: int = 0,
    limit: int = 100
) -> List[Dict[str, Any]]:
    """
    Get all voices filtered by optional parameters.

    Args:
        provider: Optional voice provider
        gender: Optional gender
        language: Optional language
        skip: Pagination offset
        limit: Number of results to return

    Returns:
        A list of voice dictionaries
    """
    try:
        logger.info(f"Fetching all voices with filters: provider={provider}, gender={gender}, language={language}")
        voices = await api_client.get_voices(provider, gender, language, skip, limit)
        return voices if isinstance(voices, list) else []
    except Exception as e:
        logger.error(f"Error fetching voices: {str(e)}")
        return []


async def update_voice(voice_id: Union[str, ObjectId], voice_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Update a voice by ID.

    Args:
        voice_id: The ID of the voice to update
        voice_data: Fields to update

    Returns:
        The updated voice dictionary
    """
    try:
        logger.info(f"Updating voice {voice_id} with data: {voice_data}")
        voice = await api_client.update_voice(voice_id, voice_data)
        return voice
    except Exception as e:
        logger.error(f"Error updating voice: {str(e)}")
        raise


async def delete_voice(voice_id: Union[str, ObjectId]) -> Dict[str, Any]:
    """
    Delete a voice by ID.

    Args:
        voice_id: The ID of the voice to delete

    Returns:
        The deleted voice dictionary
    """
    try:
        logger.info(f"Deleting voice with ID: {voice_id}")
        voice = await api_client.delete_voice(voice_id)
        return voice
    except Exception as e:
        logger.error(f"Error deleting voice: {str(e)}")
        raise
