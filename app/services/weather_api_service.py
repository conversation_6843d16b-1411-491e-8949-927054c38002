"""
Weather API service for interacting with the API server.

This module provides functions for retrieving weather-related data from the API server.
"""

import logging
from typing import Any, Dict, Union

from bson import ObjectId
from app.api.api_client import api_client

logger = logging.getLogger(__name__)


async def get_user_weather(user_id: Union[str, ObjectId]) -> Dict[str, Any]:
    """
    Get weather information for a user based on their location.

    Args:
        user_id: The ID of the user

    Returns:
        Dictionary containing weather details including city

    Raises:
        Exception if the request fails or response is invalid
    """
    try:
        logger.info(f"Fetching weather data for user ID: {user_id}")
        weather_response = await api_client.get_user_weather(user_id)

        if not isinstance(weather_response, dict):
            logger.warning(f"Unexpected response format for weather: {weather_response}")
            return {}

        logger.info(f"Weather data retrieved for user ID: {user_id}")
        return weather_response

    except Exception as e:
        logger.error(f"Error fetching weather for user {user_id}: {str(e)}")
        return {}
