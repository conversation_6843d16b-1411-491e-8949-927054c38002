import logging
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional

import aiohttp
from bson import ObjectId

from app.api import api_client
from app.constants.env_constants import API_ENDPOINT_KEY, API_SERVER_ENDPOINT

logger = logging.getLogger(__name__)

API_BASE_URL = f"{API_SERVER_ENDPOINT}/api"


async def create_appointment_function(appointment_data: Dict) -> Optional[Dict]:
    """
    Create a new appointment via the API.

    Args:
        appointment_data: Dictionary containing appointment details

    Returns:
        Dict containing the created appointment or None if failed
    """
    # Remove keys with None values to avoid sending nulls to the API
    filtered_appointment_data = {
        k: v for k, v in appointment_data.items() if v is not None
    }
    print(f"\n\nAPI Appointment data: {filtered_appointment_data}\n\n")
    try:
        async with aiohttp.ClientSession() as session:
            print("\n\nAPP URL:\n\n", f"{API_BASE_URL}/appointments")
            async with session.post(
                f"{API_BASE_URL}/appointments",
                json=filtered_appointment_data,
                headers={
                    "Content-Type": "application/json",
                    "Authorization": API_ENDPOINT_KEY,
                },
            ) as response:
                if response.status == 201 or response.status == 200:
                    result = await response.json()
                    logger.info(
                        f"Appointment created with ID: {result.get('_id')}")
                    return result
                else:
                    logger.error(
                        f"Failed to create appointment: {response.status}")
                    return None
    except Exception as e:
        logger.error(f"Error creating appointment: {str(e)}")
        return None


async def get_appointments(
    user_id: str,
    title: Optional[str] = None,
    active_only: bool = True,
) -> List[Dict]:
    """
    Retrieve appointments for a user, optionally filtered by date or title.

    Args:
        user_id: ID of the user
        date: Optional date to filter appointments
        title: Optional title to filter appointments
        active_only: If True, only return active appointments

    Returns:
        List of appointment dictionaries
    """
    try:
        params = {"user_id": user_id, "active_only": str(active_only).lower()}

        if title:
            params["title"] = title
        print("\n\nAPP URL:\n\n", f"{API_BASE_URL}/appointments")
        async with aiohttp.ClientSession() as session:
            async with session.get(
                f"{API_BASE_URL}/appointments",
                params=params,
                headers={"Authorization": API_ENDPOINT_KEY},
            ) as response:
                if response.status == 200:
                    appointments = await response.json()
                    return appointments
                else:
                    logger.error(
                        f"Failed to get appointments: {response.status}")
                    return []
    except Exception as e:
        logger.error(f"Error getting appointments: {str(e)}")
        return []


async def get_appointments_by_id(appointment_id: str) -> Optional[Dict]:
    """
    Retrieve a specific appointment by its ID.

    Args:
        appointment_id: ID of the appointment

    Returns:
        Appointment dictionary or None if not found
    """
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(
                f"{API_BASE_URL}/appointments/{appointment_id}",
                headers={"Authorization": API_ENDPOINT_KEY},
            ) as response:
                if response.status == 200:
                    appointment = await response.json()
                    return appointment
                else:
                    logger.error(
                        f"Failed to get appointment: {response.status}")
                    return None
    except Exception as e:
        logger.error(f"Error getting appointment: {str(e)}")
        return None


async def get_appointments_by_user(
    # user_id: str,
    user_id: str,
    skip: int = 0,  # Default from your API query params
    limit: int = 100,  # Default from your API query params
    title: Optional[str] = None,
    start_date: Optional[str] = None,  # Expected YYYY-MM-DD
    end_date: Optional[str] = None,  # Expected YYYY-MM-DD
    start_time: Optional[str] = None,  # Expected HH:MM
    end_time: Optional[str] = None,  # Expected HH:MM
    field: Optional[str] = None,
    active_only: bool = True,
) -> List[Dict]:
    """
    Retrieve appointments for a user, optionally filtered by date or title.

    Args:
        user_id: ID of the user
        date: Optional date to filter appointments
        title: Optional title to filter appointments
        active_only: If True, only return active appointments

    Returns:
        List of appointment dictionaries
    """
    try:
        # Prepare query parameters for the API call
        query_params: Dict[str, Any] = {
            "skip": skip,
            "limit": limit,
        }
        if title is not None:
            query_params["title"] = title
        if start_date is not None:
            query_params["start_date"] = start_date
        if end_date is not None:  # If you want to support date ranges
            query_params["end_date"] = end_date
        if start_time is not None:
            query_params["start_time"] = start_time
        if end_time is not None:  # If you want to support time ranges
            query_params["end_time"] = end_time

        if field is not None:
            query_params["field"] = field  # cab be summary, notes, description

        # Clean out None values, as some HTTP clients might send "null"
        query_params = {k: v for k, v in query_params.items() if v is not None}
        logger.info(f"get_appointments_by_user query_params: {query_params}")
        logger.info(f"APP URL:{API_BASE_URL}/appointments/user/{user_id}")
        async with aiohttp.ClientSession() as session:
            async with session.get(
                f"{API_BASE_URL}/appointments/user/{user_id}",
                params=query_params,
                headers={"Authorization": API_ENDPOINT_KEY},
            ) as response:
                if response.status == 200:
                    appointments = await response.json()

                    return appointments
                else:
                    logger.error(
                        f"Failed to get appointments: {response.status}")
                    return []
    except Exception as e:
        logger.error(f"Error getting appointments: {str(e)}")
        return []


async def search_appointments_with_title(
    user_id: str,
    title: Optional[str] = None,
    active_only: bool = True,
) -> List[Dict]:
    """
    Retrieve appointments for a user, optionally filtered by date or title.

    Args:
        user_id: ID of the user
        date: Optional date to filter appointments
        title: Optional title to filter appointments
        active_only: If True, only return active appointments

    Returns:
        List of appointment dictionaries
    """
    try:
        params = {"active_only": str(active_only).lower()}

        if title:
            params["title"] = title
        print(
            "\n\nAPP search URL:\n\n", f"{API_BASE_URL}/appointments/search/{user_id}"
        )
        async with aiohttp.ClientSession() as session:
            async with session.get(
                f"{API_BASE_URL}/appointments/search/{user_id}",
                params=params,
                headers={"Authorization": API_ENDPOINT_KEY},
            ) as response:
                if response.status == 200:
                    appointments = await response.json()
                    return appointments
                else:
                    logger.error(
                        f"Failed to get appointments: {response.status}")
                    return []
    except Exception as e:
        logger.error(f"Error getting appointments: {str(e)}")
        return []


async def update_appointment(appointment_id: str, update_data: Dict) -> Optional[Dict]:
    """
    Update an existing appointment.

    Args:
        appointment_id: ID of the appointment to update
        update_data: Dictionary with fields to update

    Returns:
        Updated appointment dictionary or None if failed
    """
    try:
        print(
            "\n\nAPP update_appointment URL:\n\n",
            f"{API_BASE_URL}/appointments/{appointment_id}",
        )
        if "description" in update_data:
            update_data["summary"] = update_data["description"]
            # del update_data["description"]

        async with aiohttp.ClientSession() as session:
            async with session.put(
                f"{API_BASE_URL}/appointments/{appointment_id}",
                json=update_data,
                headers={
                    "Content-Type": "application/json",
                    "Authorization": API_ENDPOINT_KEY,
                },
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    logger.info(
                        f"Appointment updated with ID: {appointment_id}")
                    return result
                else:
                    logger.error(
                        f"Failed to update appointment: {response.status}")
                    return None
    except Exception as e:
        logger.error(f"Error updating appointment: {str(e)}")
        return None


async def delete_appointment(appointment_id: str) -> bool:
    """
    Delete an appointment by its ID.

    Args:
        appointment_id: ID of the appointment to delete

    Returns:
        True if deleted successfully, False otherwise
    """
    try:
        logger.info(f"Deleting appointment with ID: {appointment_id}")
        async with aiohttp.ClientSession() as session:
            async with session.delete(
                f"{API_BASE_URL}/appointments/{appointment_id}",
                headers={
                    "Authorization": API_ENDPOINT_KEY,
                },
            ) as response:
                if response.status == 200 or response.status == 204:
                    logger.info(
                        f"Appointment deleted with ID: {appointment_id}")
                    return True
                else:
                    logger.error(
                        f"Failed to delete appointment: {response.status}")
                    return False
    except Exception as e:
        logger.error(f"Error deleting appointment: {str(e)}")
        return False
