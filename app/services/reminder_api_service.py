"""
Reminder API service for interacting with the API server.

This module provides functions for interacting with the reminder-related endpoints of the API server.
"""

import logging
from datetime import datetime, timedelta, timezone
from typing import Any, Dict, List, Optional, Union

import dateparser
from bson import ObjectId

from app.api.api_client import api_client

logger = logging.getLogger(__name__)


async def create_reminder(reminder_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Create a new reminder via the API.

    Args:
        reminder_data: The reminder data to create

    Returns:
        The created reminder

    Raises:
        Exception: If the API request fails
    """
    try:
        logger.info(f"Creating reminder with data: {reminder_data}")

        reminder_data["reminder_type"] = reminder_data["reminder_type"].lower()

        filtered_reminder_data = {
            k: v for k, v in reminder_data.items() if v is not None
        }
        print(
            "\n\n[REQUEST DATA] Reminder payload being sent to API:\n",
            filtered_reminder_data,
            "\n\n",
        )

        reminder = await api_client.create_reminder(filtered_reminder_data)
        return reminder
    except Exception as e:
        logger.error(f"Error creating reminder: {str(e)}")
        raise


async def get_reminder(reminder_id: Union[str, ObjectId]) -> Dict[str, Any]:
    """
    Get a reminder by ID via the API.

    Args:
        reminder_id: The ID of the reminder to get

    Returns:
        The reminder with the specified ID

    Raises:
        Exception: If the API request fails
    """
    try:
        logger.info(f"Getting reminder with ID: {reminder_id}")
        reminder = await api_client.get_reminder(reminder_id)
        return reminder
    except Exception as e:
        logger.error(f"Error getting reminder: {str(e)}")
        raise


async def get_reminders_by_user(
    user_id: Union[str, ObjectId], active_only: bool = True
) -> List[Dict[str, Any]]:
    """
    Get reminders by user ID via the API.

    Args:
        user_id: The ID of the user
        active_only: If True, only return active reminders

    Returns:
        A list of reminders for the specified user

    Raises:
        Exception: If the API request fails
    """
    try:
        logger.info(f"Getting reminders for user ID: {user_id}")
        response = await api_client.get_reminders_by_user(user_id, active_only)

        # Handle different response formats
        if isinstance(response, dict) and "data" in response:
            reminders = response["data"]
        elif isinstance(response, list):
            reminders = response
        else:
            logger.warning(f"Unexpected response format: {type(response)}")
            reminders = response if response else []

        logger.info(f"Found {len(reminders)} reminders for user ID: {user_id}")
        return reminders
    except Exception as e:
        logger.error(f"Error getting reminders for user: {str(e)}")
        # Return empty list instead of raising to avoid blocking the conversation
        logger.warning("Returning empty list due to error")
        return []


async def update_reminder(
    reminder_id: Union[str, ObjectId], reminder_data: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Update a reminder by ID via the API.

    Args:
        reminder_id: The ID of the reminder to update
        reminder_data: The reminder data to update

    Returns:
        The updated reminder

    Raises:
        Exception: If the API request fails
    """
    print(f"\n[update_reminder] Starting update for reminder_id: {reminder_id}")
    print(f"[update_reminder] Data to update: {reminder_data}")
    try:
        logger.info(f"Updating reminder with ID: {reminder_id}")
        logger.debug(f"Update data: {reminder_data}")

        reminder = await api_client.update_reminder(reminder_id, reminder_data)

        print(f"[update_reminder] Update successful. Result: {reminder}")
        return reminder
    except Exception as e:
        logger.error(f"Error updating reminder: {str(e)}")
        print(f"[update_reminder] Exception occurred: {str(e)}")
        raise
    finally:
        print("[update_reminder] Finished update_reminder execution.\n")


async def update_reminder_status(
    reminder_id: Union[str, ObjectId], is_active: bool
) -> Dict[str, Any]:
    """
    Update the active status of a reminder via the API.

    Args:
        reminder_id: The ID of the reminder to update
        is_active: The new active status

    Returns:
        The updated reminder

    Raises:
        Exception: If the API request fails
    """
    try:
        logger.info(
            f"Updating reminder status with ID: {reminder_id}, is_active: {is_active}"
        )
        reminder = await api_client.update_reminder_status(reminder_id, is_active)
        logger.info(f"Reminder status updated: {reminder.get('_id')}")
        return reminder
    except Exception as e:
        logger.error(f"Error updating reminder status: {str(e)}")
        raise


# Backward compatibility functions with error handling
# async def get_reminders(user_id: str, active_only: bool = True) -> List[Dict[str, Any]]:
#     """
#     Get all reminders for a specific user.

#     This function is maintained for backward compatibility.

#     Args:
#         user_id: The ID of the user to get reminders for
#         active_only: If True, only return active reminders

#     Returns:
#         A list of reminder dictionaries, or an empty list if the operation failed
#     """
#     try:
#         logger.info(
#             f"Getting reminders for user ID: {user_id} (active_only={active_only})"
#         )
#         reminders = await get_reminders_by_user(user_id, active_only)
#         logger.info(f"Successfully retrieved {len(reminders)} reminders")
#         return reminders
#     except Exception as e:
#         logger.error(f"Failed to get reminders: {str(e)}")
#         return []


async def get_reminders(  # Renamed from old get_reminders, or replace existing one
    user_id: str,
    skip: int = 0,  # Default from your API query params
    limit: int = 100,  # Default from your API query params
    reminder_text: Optional[str] = None,
    reminder_type: Optional[str] = None,
    start_date: Optional[str] = None,  # Expected YYYY-MM-DD
    end_date: Optional[str] = None,  # Expected YYYY-MM-DD
    start_time: Optional[str] = None,  # Expected HH:MM
    end_time: Optional[str] = None,  # Expected HH:MM
    active_only: Optional[
        bool
    ] = None,  # Optional: if API has a specific flag not covered by dates
    # Add any other specific flags your API might support, like linked_entity_id
) -> List[Dict[str, Any]]:
    print("\n\n[get_reminders] Starting get_reminders execution.")
    """
    Get reminders for a specific user, with various filtering options.

    Args:
        user_id: The ID of the user.
        skip: Number of records to skip (for pagination).
        limit: Maximum number of records to return.
        reminder_text: Text to search in reminder content.
        reminder_type: Filter by reminder type.
        start_date: Filter for reminders on or after this date (YYYY-MM-DD).
        end_date: Filter for reminders on or before this date (YYYY-MM-DD).
        start_time: Filter for reminders on or after this time on the given date(s) (HH:MM).
        end_time: Filter for reminders on or before this time on the given date(s) (HH:MM).
        active_only: (Optional) Explicitly request only active reminders if API supports it
                     beyond date/time filtering.

    Returns:
        A list of reminder dictionaries, or an empty list if the operation failed or no reminders found.
    """
    if not user_id:
        logger.error("get_reminders: user_id is required.")
        return []

    # Assuming this is your base endpoint
    endpoint = f"/api/reminders/user/{user_id}"

    # Prepare query parameters for the API call
    query_params: Dict[str, Any] = {
        "skip": skip,
        "limit": limit,
    }
    if reminder_text is not None:
        query_params["reminder_text"] = reminder_text
    if reminder_type is not None:
        query_params["reminder_type"] = reminder_type
    if start_date is not None:
        query_params["start_date"] = start_date
    if end_date is not None:  # If you want to support date ranges
        query_params["end_date"] = end_date
    if start_time is not None:
        query_params["start_time"] = start_time
    if end_time is not None:  # If you want to support time ranges
        query_params["end_time"] = end_time
    if active_only is not None:  # If your API has a distinct active_only flag
        query_params["active_only"] = (
            active_only  # Will be converted to string by _convert_objectid_to_str
        )
    else:
        query_params["active_only"] = (
            True  # Will be converted to "true" by _convert_objectid_to_str
        )

    # Clean out None values, as some HTTP clients might send "null"
    query_params = {k: v for k, v in query_params.items() if v is not None}

    logger.info(
        f"Fetching reminders for user ID: {user_id} with query params: {query_params}"
    )

    try:
        # The _request method will convert boolean and numeric values to strings
        response = await api_client._request("GET", endpoint, params=query_params)
        # Handle different response formats more robustly
        if response is None:
            logger.warning(
                f"No response received when fetching reminders for user {user_id}"
            )
            return []

        if isinstance(response, list):
            logger.info(
                f"Successfully retrieved {len(response)} reminders for user {user_id}."
            )
            return response

        if isinstance(response, dict):
            if "data" in response and isinstance(response["data"], list):
                logger.info(
                    f"Successfully retrieved {len(response['data'])} reminders for user {user_id} from 'data' key."
                )
                return response["data"]
            else:
                # If it's a dict but doesn't have a data key, it might be a single reminder
                logger.info(f"Retrieved a single reminder for user {user_id}")
                return [response]

        # If we get here, the response format is unexpected
        logger.info(f"Response type: {type(response)}, Response: {response}")
        return []

    except Exception as e:
        print(f"get_reminders: Exception occurred: {str(e)}")
        logger.error(
            f"Error fetching reminders for user_id {user_id}. Params: {query_params}"
        )
        return []


async def search_reminder_with_text(
    user_id: str,
    reminder_text: Optional[str] = None,
    active_only: bool = True,
) -> List[Dict]:
    """
    Search for reminders containing specific text for a user.

    Args:
        user_id: ID of the user
        reminder_text: Text to search for in reminders
        active_only: If True, only return active reminders

    Returns:
        List of reminder dictionaries
    """
    try:
        logger.info(
            f"Searching for reminders with text '{reminder_text}' for user {user_id}"
        )
        response = await api_client.find_with_reminder_text(
            user_id, reminder_text, active_only
        )

        # Handle different response formats
        if isinstance(response, dict) and "data" in response:
            reminders = response["data"]
        elif isinstance(response, list):
            reminders = response
        else:
            logger.warning(f"Unexpected response format: {type(response)}")
            reminders = response if response else []

        logger.info(f"Found {len(reminders)} reminders matching '{reminder_text}'")
        return reminders
    except Exception as e:
        logger.error(f"Error searching reminders with text: {str(e)}")
        return []


async def get_reminders_by_linked_entity(
    user_id: str, linked_entity: str
) -> List[Dict[str, Any]]:
    """
    Get all reminders for a specific user with linked entity.

    This function is maintained for backward compatibility.

    Args:
        user_id: The ID of the user to get reminders for
        linked_entity : The ID of the linked entity to get reminders for

    Returns:
        A list of reminder dictionaries, or an empty list if the operation failed
    """
    try:
        return await api_client.get_appointments_with_linked_entity(
            user_id, linked_entity
        )
    except Exception as e:
        logger.error(f"Failed to get reminders: {str(e)}")
        return []


async def get_reminder_by_id(
    reminder_id: str, linked_entity: Optional[str] = None
) -> List[Dict[str, Any]]:
    """
    Get reminder by reminder id

    This function is maintained for backward compatibility.

    Args:
        user_id: The ID of the user to get reminders for
        linked_entity : The ID of the linked entity to get reminders for

    Returns:
        A list of reminder dictionaries, or an empty list if the operation failed
    """
    try:
        return await api_client.get_reminder(reminder_id)
    except Exception as e:
        logger.error(f"Failed to get reminders: {str(e)}")
        return []


async def delete_reminder(reminder_id: str) -> Dict[str, Any]:
    """
    Delete a reminder by ID via the API.

    Args:
        reminder_id: The ID of the reminder to delete

    Returns:
        The deleted reminder

    Raises:
        Exception: If the API request fails
    """
    try:
        logger.info(f"Deleting reminder with ID: {reminder_id}")
        reminder = await api_client.delete_reminder(reminder_id)
        return reminder
    except Exception as e:
        logger.error(f"Error deleting reminder: {str(e)}")
        raise
