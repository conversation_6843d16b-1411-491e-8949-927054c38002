"""
User API service for interacting with the API server.

This module provides functions for interacting with the user-related endpoints of the API server.
"""

import logging
from typing import Any, Dict, Optional, Union

from bson import ObjectId

from app.api.api_client import api_client

logger = logging.getLogger(__name__)


async def create_user(user_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Create a new user via the API.

    Args:
        user_data: The user data to create

    Returns:
        The created user

    Raises:
        Exception: If the API request fails
    """
    try:
        logger.info(f"Creating user with data: {user_data}")
        user = await api_client.create_user(user_data)
        logger.info(f"User created with ID: {user.get('_id')}")
        return user
    except Exception as e:
        logger.error(f"Error creating user: {str(e)}")
        raise


async def get_user(user_id: Union[str, ObjectId]) -> Dict[str, Any]:
    """
    Get a user by ID via the API.

    Args:
        user_id: The ID of the user to get

    Returns:
        The user with the specified ID or an empty dict if the operation failed

    Raises:
        No exceptions are raised; errors are logged and an empty dict is returned
    """
    try:
        logger.info(f"Getting user with ID: {user_id}")
        response = await api_client.get_user(user_id)

        # Check if the response is valid
        if response and isinstance(response, dict):
            logger.info(f"Successfully retrieved user data for ID: {user_id}")
            return response
        else:
            logger.warning(
                f"Unexpected response format for user {user_id}: {type(response)}"
            )
            return {}
    except Exception as e:
        logger.error(f"Error getting user: {str(e)}")
        return {}


# async def get_user_by_voice_id(voice_id: Union[str, ObjectId]) -> Dict[str, Any]:
#     """
#     Get a user by voice ID via the API.

#     Args:
#         voice_id: The voice ID of the user to get

#     Returns:
#         The user with the specified voice ID

#     Raises:
#         Exception: If the API request fails
#     """
#     try:
#         logger.info(f"Getting user with voice ID: {voice_id}")
#         user = await api_client.get_user_by_voice_id(voice_id)
#         return user
#     except Exception as e:
#         logger.error(f"Error getting user by voice ID: {str(e)}")
#         raise


async def update_user(
    user_id: Union[str, ObjectId], user_data: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Update a user by ID via the API.

    Args:
        user_id: The ID of the user to update
        user_data: The user data to update

    Returns:
        The updated user

    Raises:
        Exception: If the API request fails
    """
    try:
        logger.info(f"Updating user with ID: {user_id}")
        logger.debug(f"Update data: {user_data}")
        user = await api_client.update_user(user_id, user_data)
        logger.info(f"User updated: {user.get('_id')}")
        return user
    except Exception as e:
        logger.error(f"Error updating user: {str(e)}")
        raise


async def save_or_update_user(
    user_data: Dict[str, Any],
    participant_id: Optional[str] = None,
    user_id: Optional[Union[str, ObjectId]] = None,
) -> Dict[str, Any]:
    """
    Save or update a user via the API.

    If user_id is provided, updates the existing user.
    If participant_id is provided but user_id is not, stores the participant_id in the user data.
    Otherwise, creates a new user.

    Args:
        user_data: The user data to save or update
        participant_id: Optional participant ID to store with the user
        user_id: Optional user ID to update an existing user

    Returns:
        The created or updated user

    Raises:
        Exception: If the API request fails
    """
    try:
        logger.info(f"Updating existing user with ID: {user_id}")
        logger.info(f"User data to save: {user_data}")
        result = await update_user(user_id, user_data)
        logger.info(f"User update result: {result}")
        return result

    except Exception as e:
        logger.error(f"Error saving or updating user: {str(e)}", exc_info=True)
        raise
