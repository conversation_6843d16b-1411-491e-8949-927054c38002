"""
Contact API service for interacting with the API server.

This module provides functions for interacting with the contact-related endpoints of the API server.
"""

import logging
from datetime import datetime
from typing import Any, Dict, List, Optional, Union

from bson import ObjectId

from app.api.api_client import api_client
from app.utils.transform_data import transform_contact_data

logger = logging.getLogger(__name__)


async def create_contact(contact_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Create a new contact via the API.

    Args:
        contact_data: The contact data to create

    Returns:
        The created contact

    Raises:
        Exception: If the API request fails
    """
    try:
        logger.info(f"Creating contact with data: {contact_data}")
        transformed_data = transform_contact_data(contact_data)
        logger.info(f"Transformed contact data: {transformed_data}")
        contact = await api_client.create_contact(transformed_data)
        logger.info(f"Contact created with ID: {contact.get('_id')}")
        return contact
    except Exception as e:
        logger.error(f"Error creating contact: {str(e)}")
        raise


async def search_contacts_by_name(user_id: str, name: str) -> list[dict]:
    """
    Search for contacts by user_id and name (case-insensitive, matches anywhere in name).

    Args:
        user_id: The ID of the user whose contacts to search
        name: The name (or part of name) to search for

    Returns:
        A list of matching contacts

    Raises:
        Exception: If the API request fails
    """
    try:
        return await api_client.search_contacts_by_name(user_id, name)
    except Exception as e:
        logger.error(f"Error searching contacts: {str(e)}")
        raise


def search_contact_with_name(user_id: str, name: str) -> list[dict]:
    """
    Search for contacts by user_id and name (case-insensitive, matches anywhere in name).

    Args:
        user_id: The ID of the user whose contacts to search
        name: The name (or part of name) to search for

    Returns:
        A list of matching contacts

    Raises:
        Exception: If the API request fails
    """
    print(f"\n\nsearch_contact_with_name called: {name}\n\n")
    try:
        return api_client.search_contacts_by_name(user_id, name)
    except Exception as e:
        logger.error(f"Error searching contacts: {str(e)}")
        raise


async def get_contact(contact_id: Union[str, ObjectId]) -> Dict[str, Any]:
    """
    Get a contact by ID via the API.

    Args:
        contact_id: The ID of the contact to get

    Returns:
        The contact with the specified ID

    Raises:
        Exception: If the API request fails
    """
    try:
        logger.info(f"Getting contact with ID: {contact_id}")
        contact = await api_client.get_contact(contact_id)
        return contact
    except Exception as e:
        logger.error(f"Error getting contact: {str(e)}")
        raise


async def get_contacts_by_user(user_id: Union[str, ObjectId]) -> List[Dict[str, Any]]:
    """
    Get contacts by user ID via the API.

    Args:
        user_id: The ID of the user

    Returns:
        A list of contacts for the specified user

    Raises:
        Exception: If the API request fails
    """
    try:
        logger.info(f"Getting contacts for user with ID: {user_id}")
        contacts = await api_client.get_contacts_by_user(user_id)
        return contacts
    except Exception as e:
        logger.error(f"Error getting contacts by user ID: {str(e)}")
        raise


async def update_contact(
    contact_id: Union[str, ObjectId], contact_data: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Update a contact by ID via the API.

    Args:
        contact_id: The ID of the contact to update
        contact_data: The contact data to update

    Returns:
        The updated contact

    Raises:
        Exception: If the API request fails
    """
    try:
        logger.info(f"Updating contact with ID: {contact_id}")
        transformed_data = transform_contact_data(contact_data)
        logger.info(f"Transformed contact data: {transformed_data}")
        contact = await api_client.update_contact(contact_id, transformed_data)
        logger.info(f"Contact updated: {contact.get('_id')}")
        return contact
    except Exception as e:
        logger.error(f"Error updating contact: {str(e)}")
        raise


async def save_or_update_contact(
    user_id: Union[str, ObjectId],
    contact_data: Dict[str, Any],
    contact_id: Optional[Union[str, ObjectId]] = None,
    force_create_new: bool = False,
) -> Dict[str, Any]:
    """
    Save or update a contact via the API.

    If contact_id is provided, updates the existing contact.
    If force_create_new is True, always creates a new contact.
    Otherwise, tries to find an existing contact with the same name and phone number,
    or creates a new contact if none is found.

    Args:
        user_id: The ID of the user the contact belongs to
        contact_data: The contact data to save or update
        contact_id: Optional ID of an existing contact to update
        force_create_new: If True, always create a new contact even if one with the same name/phone exists

    Returns:
        The created or updated contact

    Raises:
        Exception: If the API request fails
    """
    try:
        # Make sure user_id is included in the contact data and is a string
        if isinstance(user_id, ObjectId):
            contact_data["user_id"] = str(user_id)
        else:
            # If it's already a string, use it as is
            # If it's something else, convert it to string
            contact_data["user_id"] = str(user_id)

        # Format birthday if present
        if "birthday" in contact_data and isinstance(
            contact_data["birthday"], datetime
        ):
            contact_data["birthday"] = contact_data["birthday"].isoformat()

        logger.info(f"Saving or updating contact with data: {contact_data}")
        transformed_data = transform_contact_data(contact_data)
        logger.info(f"Transformed contact data: {transformed_data}")

        # If force_create_new is True, always create a new contact
        if force_create_new:
            logger.info(f"Forcing creation of new contact for user: {user_id}")
            return await create_contact(transformed_data)

        if contact_id:
            # If contact_id is provided, update the existing contact
            # Make sure contact_id is a string
            contact_id_str = (
                str(contact_id) if isinstance(
                    contact_id, ObjectId) else str(contact_id)
            )
            logger.info(f"Updating existing contact with ID: {contact_id_str}")
            return await update_contact(contact_id_str, transformed_data)

    except Exception as e:
        logger.error(f"Error saving or updating contact: {str(e)}")
        raise


async def get_myhoudini_contacts(user_id: str, relation: Optional[str] = None) -> dict:
    """
    Fetch contacts for the given user where "is_myhoudini" is True.
    Optionally filters by relation:
      - "all": returns all contacts with is_myhoudini True.
      - "present": filters contacts to those whose relation equals "present" (case-insensitive).
    """
    try:
        logger.info(
            f"Getting MyHoudini contacts for user with ID: {user_id}, {relation}"
        )
        if relation in ["father", "mother", "parents"]:
            relation = "parent"
        contacts = await api_client.get_houdini_contacts(user_id, relation)
        return contacts
    except Exception as e:
        logger.error(
            f"Error fetching MyHoudini contacts for user {user_id}: {e}")
        raise


async def get_myhoudini_contacts_relationships(
    user_id: str, relation: Optional[str] = None
) -> dict:
    """
    Fetch contacts for the given user where "is_myhoudini" is True.
    Optionally filters by relation:
      - "all": returns all contacts with is_myhoudini True.
      - "present": filters contacts to those whose relation equals "present" (case-insensitive).
    """
    try:
        logger.info(
            f"Getting MyHoudini contacts for user with ID: {user_id}, {relation}"
        )

        if relation in ["father", "mother", "parents"]:
            relation = "parent"

        contacts = await get_myhoudini_contacts(user_id, relation)
        # use set for relationships to avoide the duplicate
        relationships = set()
        for contact in contacts:
            if contact["relationship"]:
                relationships.add(contact["relationship"])
            # relationships.add(contact["relationship"])

        # return the relatioship as list
        print(
            "\n\nget_myhoudini_contacts_relationships relationships: \n\n",
            relationships,
        )
        relationships = list(relationships)
        print(
            "\n\nget_myhoudini_contacts_relationships relationships: \n\n",
            relationships,
        )

        return relationships
    except Exception as e:
        logger.error(
            f"Error fetching MyHoudini contacts for user {user_id}: {e}")
        raise
