import asyncio
import re
from datetime import datetime, timedelta, timezone
from typing import Any, Dict, List, Optional

from dateutil.parser import parse as dateutil_parse  # For parsing date strings
from dateutil.rrule import rrulestr as dateutil_rrulestr
from livekit.agents import ToolError, function_tool

# Import your application modules
from app.constants.action_types import ActionType
from app.core.logger_setup import logger

# Import API service functions
from app.services import reminder_api_service

# Import SessionData and UserDataContext from the new module
from app.state.session_data import UserDataContext

# Import utilities
from app.utils.event_send import send_event  # Needs room, adjust to take room from ctx
from app.utils.time_utils import (
    _prepare_api_date,
    _prepare_api_time,
    convert_local_to_utc,
    get_user_timezone,
)


def get_user_state_from_context(ctx: UserDataContext):
    """Helper function to get user state from context with robust access pattern."""
    if not ctx.session or not ctx.session.userdata:
        raise ToolError("Internal session error.")

    # Try attribute access first
    if hasattr(ctx.session.userdata, "user_state"):
        user_state = ctx.session.userdata.user_state
    # Fall back to dictionary access
    elif (
        isinstance(ctx.session.userdata, dict)
        and "session_data" in ctx.session.userdata
    ):
        user_state = ctx.session.userdata["session_data"].user_state
    else:
        raise ToolError("Unable to access user state from session data.")

    if not user_state:
        raise ToolError("User state not available.")

    return user_state


def local_to_utc_datetime_in_tool(
    date_str, time_str, user_timezone_obj, default_time="00:00"
):
    """
    Helper to convert local date/time strings to UTC datetime object.
    user_timezone_obj should be a pytz or zoneinfo object.
    """
    dt_str = f"{date_str} {time_str or default_time}"
    local_dt = dateutil_parse(dt_str)
    if local_dt.tzinfo is None:  # Naive datetime
        if hasattr(user_timezone_obj, "localize") and callable(
            user_timezone_obj.localize
        ):
            local_dt = user_timezone_obj.localize(local_dt)  # For pytz
        else:
            local_dt = local_dt.replace(
                tzinfo=user_timezone_obj
            )  # For zoneinfo or basic tzinfo
    else:  # Aware datetime, ensure it's in the target user_timezone
        local_dt = local_dt.astimezone(user_timezone_obj)
    return local_dt.astimezone(timezone.utc)


@function_tool
async def find_reminder(
    ctx: UserDataContext,  # Standard type hint for tools in livekit-agents is JobContext
    # If you use UserDataContext, ensure it's properly defined & imported
    reminder_text_query: Optional[str] = None,
    date_query: Optional[str] = None,
    time_query: Optional[str] = None,
    reminder_type_query: Optional[str] = None,
) -> str:  # The final return should be a string for the LLM
    """
    Finds reminders for the user based on various criteria.
    Includes a polite message if the search takes a noticeable amount of time.
    (Your existing detailed docstring here)

    The LLM should ideally follow this interaction flow:
    1. If the user says "find reminder" without specifics:
       - Ask for the reminder name/text (reminder_text_query).
    2. If the user doesn't provide text but provides date/time:
       - Proceed with date/time search.
    3. If the user provides only time (time_query):
       - This tool will assume the current date.
    4. All searches default to future reminders from the current moment,
       unless a specific past date is explicitly queried.

    IMPORTANT TIME HANDLING GUIDELINES:
    1. For date_query, accept natural language like "today", "tomorrow", "next week",
       or specific dates like "July 10" or "2025-07-10".
    2. For time_query, accept natural language like "morning", "afternoon", "evening",
       or specific times like "3pm", "15:00", "9:30 AM".
    3. If the user provides a vague time reference, ask for clarification before
       calling this function.
    4. The system will automatically handle timezone conversion based on the user's
       configured timezone.
    5. When searching for reminders at a specific time that has already passed today,
       the system will still return matching reminders (unlike setting reminders).
    6. If date_query is "today" and time_query is not provided, search from the current time till end of day.
    7. If date_query is "today" and time_query is provided, search from that time onwards.
    8. If date_query is "tomorrow" or a future date and time_query is not provided, search from the beginning of that day.

    NOTE: Unlike appointments, reminders do NOT have a notify_before parameter.
    Do NOT ask users about notification time for reminders.
    Do not mention Ref: ... to users

    Args:
        ctx: The user data context, providing access to user_state and session data.
        reminder_text_query: (Optional) Text to search within the reminder content.
        date_query: (Optional) A string representing the date (e.g., "today", "tomorrow", "2025-07-10").
        time_query: (Optional) A string representing the time (e.g., "3pm", "morning", "15:00").
        reminder_type_query: (Optional) The type of reminder (e.g., "medication", "general").

    Returns:
        A string listing found reminders, a message if none are found, or an error message.
        Stores found reminders in `ctx.session.userdata.current_reminder_search_results`.
    """
    # Get user state with robust access pattern
    user_state = get_user_state_from_context(ctx)
    if not user_state.user_id:
        raise ToolError("User ID not available for finding reminders.")

    logger.info(
        f"find_reminder called. User: {user_state.user_id}. Text: '{reminder_text_query}', "
        f"Date: '{date_query}', Time: '{time_query}', Type: '{reminder_type_query}'"
    )

    # --- Latency Management ---
    # Create a task that will send a status message if the search takes longer than expected
    from app.utils.latency_management import (
        cleanup_status_task,
        create_status_update_task,
    )

    status_update_task = await create_status_update_task(
        ctx,
        delay=0.04,  # 40 milliseconds - VERY SHORT!
        message="Just a moment while I look that up for you...",
        operation_name="find_reminder",
    )
    # --- End Latency Management Setup ---

    try:
        # --- Your Existing find_reminder Logic ---
        user_timezone = get_user_timezone(
            user_state
        )  # This should return a zoneinfo or pytz object
        logger.info(f"find_reminder: Using timezone: {user_timezone}")
        now_for_parsing = datetime.now(user_timezone)

        if (
            not reminder_text_query
            and not date_query
            and not time_query
            and not reminder_type_query
        ):
            date_query = "today"
            # Use current time for defaulting only if no other criteria
            time_query = now_for_parsing.strftime(
                "%H:%M"
            )  # Ensure this format is handled by _prepare_api_time
            logger.info(
                f"No search criteria. Defaulting to today at current time ({time_query})"
            )

        api_params: Dict[str, Any] = {
            "user_id": user_state.user_id,
            "skip": 0,
            "limit": 100,
            "active_only": True,
        }

        if reminder_text_query:
            api_params["reminder_text"] = reminder_text_query
        if reminder_type_query:
            api_params["reminder_type"] = reminder_type_query

        api_start_date_str = _prepare_api_date(date_query, now_for_parsing)
        api_start_time_str = _prepare_api_time(
            time_query
        )  # Ensure this handles varied time inputs
        # This section implements the logic for determining the date/time range for the API call
        # based on the user's query, aligning with the specified cases:
        # - Case 1 (Implicit): If only reminder_text_query is given, this date/time logic defaults to Case 5.
        #   The LLM is expected to ask for reminder_text if none is provided initially.
        # - Case 2 (Implicit): If no reminder_text_query, but date/time is given, it's handled by api_start_date_str or api_start_time_str blocks.
        #   The LLM might ask for date/time if only reminder_text is vague.
        # - Case 3: User provides reminder_text + date_query and/or time_query.
        #           Handled by `if api_start_date_str:` and `elif api_start_time_str:`.
        # - Case 4: Only time_query provided. Handled by `elif api_start_time_str:`. Date defaults to today.
        # - Case 5: No date_query or time_query. Handled by the final `else:` block. Defaults to future from now.
        # All searches aim for future reminders unless a specific past date is explicitly part of date_query.

        if api_start_date_str:
            # A specific date (past, present, or future) was provided or parsed.
            api_params["start_date"] = api_start_date_str
            if api_start_time_str:
                # User provided both date and time (part of Case 3).
                # API will use this specific date and time.
                api_params["start_time"] = api_start_time_str
            else:
                # User provided only date, no specific time (part of Case 3 or standalone date query).
                # Behavior depends on whether the date is past, today, or future.
                # If this date is today, search from the current time onwards.
                # If this date is in the past, search from the beginning of that day (no start_time).
                # If this date is in the future, search from the beginning of that day (no start_time).
                try:
                    parsed_date_obj = datetime.strptime(
                        api_start_date_str, "%Y-%m-%d"
                    ).date()
                    if parsed_date_obj == now_for_parsing.date():
                        api_params["start_time"] = now_for_parsing.strftime("%H:%M")
                        logger.info(
                            f"Date is today, no time. API start_time: {api_params['start_time']}"
                        )
                    # Other conditions for past/future date search ranges (as per your original logic)
                except ValueError:
                    logger.warning(
                        f"Could not parse api_start_date_str '{api_start_date_str}' for time check."
                    )
        elif api_start_time_str:
            # Date defaults to today. Time is as specified.
            # Search will be for the specified time on today. If this time has passed,
            # the API's active_only/range logic should ensure only future instances are found if applicable.
            api_params["start_time"] = api_start_time_str
            # Default date to today for the API call.
            api_params["start_date"] = now_for_parsing.strftime("%Y-%m-%d")
            logger.info(
                f"Time query, no date. API start_date: {api_params['start_date']}"
            )
        else:  # Default to current moment if no specific date/time query parts led to params
            if (
                "start_date" not in api_params
            ):  # Only if not already set by default above
                api_params["start_date"] = now_for_parsing.strftime("%Y-%m-%d")
                api_params["start_time"] = now_for_parsing.strftime("%H:%M")
                logger.info(
                    f"Defaulting search from current moment: {api_params['start_date']} {api_params['start_time']}"
                )

        # Always set end_date and end_time for a clear search window
        if "start_date" in api_params:
            if "end_date" not in api_params:
                api_params["end_date"] = api_params["start_date"]
            # Always set end_time to 23:59 for the end of the day
            api_params["end_time"] = "23:59"
            logger.info(f"Setting end_time to 23:59 for date {api_params['end_date']}")

        # UTC conversion (using the in-tool helper for clarity)
        if (
            "start_date" in api_params
        ):  # Ensure start_date exists before trying to use it
            start_time_to_convert = api_params.get("start_time")  # Can be None
            utc_start = local_to_utc_datetime_in_tool(
                api_params["start_date"], start_time_to_convert, user_timezone
            )
            api_params["start_date"] = utc_start.strftime("%Y-%m-%d")
            api_params["start_time"] = utc_start.strftime("%H:%M")

        if "end_date" in api_params:  # Ensure end_date exists
            # Always use 23:59 as the end time for consistent behavior
            utc_end = local_to_utc_datetime_in_tool(
                api_params["end_date"],
                "23:59",  # Always use 23:59 (end of day) for end_time
                user_timezone,
                default_time="23:59",
            )
            api_params["end_date"] = utc_end.strftime("%Y-%m-%d")
            api_params["end_time"] = utc_end.strftime("%H:%M")
            logger.info(f"Converted end time 23:59 to UTC: {api_params['end_time']}")

        final_api_params = {k: v for k, v in api_params.items() if v is not None}
        logger.info(
            f"Calling reminder_api_service.get_reminders with final params: {final_api_params}"
        )

        reminders_from_api: List[Dict[str, Any]] = (
            await reminder_api_service.get_reminders(**final_api_params)
        )
        # --- Main work done, clean up status update task ---
        await cleanup_status_task(status_update_task)
        # ---

        if not reminders_from_api:
            logger.info(f"No reminders found with parameters: {final_api_params}")
            if reminder_text_query:
                return f"No reminders found containing '{reminder_text_query}'. Would you like to create one?"
            elif date_query:
                return f"No reminders found for {date_query}. Would you like to create one for this date?"
            else:
                return "No reminders found matching your criteria. Fancy creating a new reminder?"

        # Handle session data storage based on structure
        if hasattr(ctx.session.userdata, "current_reminder_search_results"):
            ctx.session.userdata.current_reminder_search_results = reminders_from_api
        elif isinstance(ctx.session.userdata, dict):
            ctx.session.userdata["current_reminder_search_results"] = reminders_from_api
        response_lines = []  # This should be a list of strings
        # ... (Your existing logic for formatting response_lines)
        # Example:
        if len(reminders_from_api) > 5:
            response_lines.append(
                f"I found {len(reminders_from_api)} reminders matching your search."
            )
        else:
            response_lines.append(
                f"Here are the reminders I found ({len(reminders_from_api)}):"
            )

        for i, r_data in enumerate(reminders_from_api):
            if i >= 5 and len(reminders_from_api) > 5:
                response_lines.append(
                    f"...and {len(reminders_from_api) - 5} more. Would you like me to list them all or narrow it down?"
                )
                break
            r_id_full = str(r_data.get("_id") or r_data.get("id", "UnknownID"))
            r_id_snip = r_id_full[-4:] if len(r_id_full) >= 4 else r_id_full
            r_text = r_data.get("reminder_text", "Untitled Reminder")
            r_time_str_utc = r_data.get("trigger_time", "No time set")
            r_type_str = r_data.get("reminder_type", "general")
            r_time_display = r_time_str_utc
            try:
                r_time_dt_utc = datetime.fromisoformat(
                    r_time_str_utc.replace("Z", "+00:00")
                )
                if r_time_dt_utc.tzinfo is None:
                    r_time_dt_utc = r_time_dt_utc.replace(tzinfo=timezone.utc)
                r_time_dt_local = r_time_dt_utc.astimezone(user_timezone)
                r_time_display = r_time_dt_local.strftime("%I:%M %p on %b %d, %Y")
            except Exception as e_time_parse:
                logger.warning(
                    f"Could not parse reminder trigger_time '{r_time_str_utc}' for display: {e_time_parse}"
                )

            response_lines.append(
                f"{i+1}. '{r_text}' ({r_type_str}) for {r_time_display}. (Ref: ...{r_id_snip})"
            )

        # The tool must return a single string to the LLM.
        return "\n".join(response_lines)

    except ToolError as te:
        # Clean up the status update task in case of error
        await cleanup_status_task(status_update_task)
        logger.warning(f"find_reminder ToolError: {te}")
        raise te  # Re-raise ToolError for the agent framework
    except Exception as e:
        # Clean up the status update task in case of error
        await cleanup_status_task(status_update_task)
        logger.error(
            f"Error in find_reminder for user {user_state.user_id}: {e}", exc_info=True
        )
        return f"Sorry, I encountered an unexpected problem while trying to find your reminders: {str(e)}"


@function_tool
async def set_reminder(
    ctx: UserDataContext,  # This is RunContext[SessionData]
    reminder_text: str,
    time_description: str,
    description: Optional[str] = None,
    is_recurring: Optional[bool] = None,
    recurrence_rule_str: Optional[str] = None,
    linked_entity: Optional[str] = None,
) -> str:
    """
    Creates a new reminder for the user.

    This function automatically determines the reminder type (medication or general)
    based on the reminder text content. It handles timezone conversion and ensures
    that reminder times are set for the future.

    IMPORTANT TIME HANDLING GUIDELINES:
    1. If the time_description is unclear or ambiguous, ASK the user for clarification
       before calling this function. Request a specific format like "4pm today",
       "every day at 8am", or "Monday at 3pm".
    2. For past times on the current day, the system will reject the reminder.
       Instead, suggest setting it for tomorrow or a future date.
    3. For recurring reminders with times that would be in the past for the current day,
       the system will automatically set them for the next occurrence.
    4. Always validate that dates are in the future before calling this function.
    5. Use ISO 8601 format for explicit dates (e.g., "2025-04-23T08:30:00").

    NOTE: Unlike appointments, reminders do NOT have a notify_before parameter.
    Do NOT ask users about notification time for reminders.

    Args:
        reminder_text: The text content of the reminder.
        time_description: Natural language description of when the reminder should trigger (e.g., "tomorrow at 3pm").
        ctx: The user data context.
        is_recurring: (Optional) Whether the reminder should recur.
        recurrence_rule_str: (Optional) The recurrence rule string (e.g., "FREQ=DAILY").
        linked_entity: (Optional) ID of an entity this reminder is linked to.
        description: (Optional) Additional details or context for the reminder.

    Returns:
        A confirmation message with details about the created reminder.
    """
    logger.info(f"Setting reminder: '{reminder_text}' at '{time_description}'")
    # Get user state with robust access pattern
    user_state = get_user_state_from_context(ctx)
    user_id = user_state.user_id
    if not user_id:
        raise ToolError(
            "I can't set a reminder without knowing who you are. Please complete the initial setup."
        )

    try:
        # Get the user's timezone
        user_timezone = get_user_timezone(user_state)
        logger.info(f"set_reminder: Using timezone: {user_timezone}")

        # Convert local time to UTC using our utility function
        trigger_time_dt, is_recurring_parsed, recurrence_rule_parsed = (
            convert_local_to_utc(
                time_description, user_timezone, is_recurring, recurrence_rule_str
            )
        )

        # Store the parsed time in the session for reference
        if trigger_time_dt:
            start_time_local = trigger_time_dt.astimezone(user_timezone)
            parsed_time_data = {
                "utc": trigger_time_dt,
                "local": start_time_local,
                "formatted": start_time_local.strftime("%I:%M %p on %b %d"),
                "original_desc": time_description,
            }
            # Handle session data storage based on structure
            if hasattr(ctx.session.userdata, "parsed_reminder_time"):
                ctx.session.userdata.parsed_reminder_time = parsed_time_data
            elif isinstance(ctx.session.userdata, dict):
                ctx.session.userdata["parsed_reminder_time"] = parsed_time_data
            # Log the parsed time for debugging
            logger.info(
                f"Parsed time '{time_description}' to {start_time_local.strftime('%I:%M %p on %b %d')}"
            )
        else:
            raise ToolError(
                "I couldn't understand the time for the reminder. Could you please specify when (e.g., 'tomorrow at 10 AM', 'in 2 hours')?"
            )

        # Check if the time description contains recurring keywords
        recurring_keywords = [
            "daily",
            "weekly",
            "monthly",
            "every",
            "each",
            "recurring",
        ]
        contains_recurring_keywords = any(
            keyword in time_description.lower() for keyword in recurring_keywords
        )

        # Use the parsed recurrence information from convert_local_to_utc
        is_recurring_time = is_recurring_parsed or (
            is_recurring is not None and is_recurring
        )
        recurrence_rule = recurrence_rule_parsed or recurrence_rule_str

        # Get current time in UTC for comparison
        now_utc = datetime.now(timezone.utc)

        # If it's a recurring reminder and time has passed for today, adjust to the next occurrence
        if trigger_time_dt <= now_utc:
            if is_recurring_time or recurrence_rule or contains_recurring_keywords:
                # For recurring reminders, adjust to the appropriate next occurrence
                logger.info(
                    f"Recurring reminder time {trigger_time_dt} is in the past, adjusting to next occurrence"
                )

                # If it contains "friday" or other day names, adjust to the next occurrence of that day
                day_keywords = {
                    "monday": 0,
                    "tuesday": 1,
                    "wednesday": 2,
                    "thursday": 3,
                    "friday": 4,
                    "saturday": 5,
                    "sunday": 6,
                }

                time_desc_lower = time_description.lower()
                found_day = None
                for day, day_num in day_keywords.items():
                    if day in time_desc_lower:
                        found_day = day_num
                        break

                if found_day is not None:
                    # Calculate days until the next occurrence of the specified day
                    current_day = now_utc.weekday()
                    days_until_next = (found_day - current_day) % 7
                    if days_until_next == 0:  # If it's the same day but time has passed
                        days_until_next = 7  # Go to next week

                    # Adjust to the next occurrence of the specified day
                    trigger_time_dt = trigger_time_dt + timedelta(days=days_until_next)
                    logger.info(
                        f"Adjusted to next {list(day_keywords.keys())[found_day]}: {trigger_time_dt}"
                    )

                    # Set recurrence rule for weekly on this day if not already set
                    if not recurrence_rule:
                        recurrence_rule = f"FREQ=WEEKLY;BYDAY={list(day_keywords.keys())[found_day][:2].upper()}"
                        is_recurring_time = True
                else:
                    # For daily or unspecified recurring patterns, just add one day
                    trigger_time_dt = trigger_time_dt + timedelta(days=1)
                    logger.info(f"Adjusted trigger time to: {trigger_time_dt}")

                    # Set recurrence rule for daily if not already set
                    if not recurrence_rule and contains_recurring_keywords:
                        recurrence_rule = "FREQ=DAILY"
                        is_recurring_time = True
            else:
                # For non-recurring reminders, still require a future time
                raise ToolError(
                    f"The time '{time_description}' (parsed as {trigger_time_dt.strftime('%I:%M %p on %b %d')}) is in the past. Please provide a future time for the reminder."
                )

        # Set final values for recurrence
        final_is_recurring = (
            is_recurring if is_recurring is not None else is_recurring_time
        )
        final_recurrence_rule = (
            recurrence_rule_str if recurrence_rule_str is not None else recurrence_rule
        )

        medication_keywords = [
            "medicine",
            "medication",
            "pill",
            "tablet",
            "capsule",
            "dose",
            "drug",
            "prescription",
            "med",
            "meds",
            "take my",
            "vitamin",
            "supplement",
        ]
        reminder_type = (
            "medication"
            if any(k in reminder_text.lower() for k in medication_keywords)
            else "general"
        )
        # ask for description
        if not description:
            description = "No description provided"

        api_payload = {
            "user_id": user_id,  # Corrected: "user_id " had an extra space
            "reminder_text": reminder_text,
            "trigger_time": trigger_time_dt.isoformat(),
            "reminder_type": reminder_type,
            "is_recurring": final_is_recurring,
            "recurrence_rule": final_recurrence_rule,
            "alert_method": "voice",
        }
        if linked_entity:
            api_payload["linked_entity"] = linked_entity
        if description is not None:
            api_payload["description"] = description

        created_reminder = await reminder_api_service.create_reminder(api_payload)

        if created_reminder:
            # Create the structured event_data format
            reminder_id = created_reminder.get("_id") or created_reminder.get("id", "")
            event_data = {
                "reminderId": str(reminder_id),
                "scheduledTime": api_payload.get("trigger_time"),
                "details": reminder_text,
                "type": reminder_type,
                "isRecurring": bool(final_is_recurring),
                "recurrenceRule": final_recurrence_rule if final_is_recurring else None,
            }

            # Handle room access with robust pattern
            room = None
            if hasattr(ctx.session.userdata, "room"):
                room = ctx.session.userdata.room
            elif (
                isinstance(ctx.session.userdata, dict)
                and "room" in ctx.session.userdata
            ):
                room = ctx.session.userdata["room"]

            if room:
                await send_event(
                    room=room,
                    action_type=ActionType.SET_REMINDER,
                    message=f"Reminder set: '{reminder_text}'",
                    data=event_data,
                )
            # Format confirmation with original time description if different
            # Handle session data access based on structure
            parsed_time = None
            if hasattr(ctx.session.userdata, "parsed_reminder_time"):
                parsed_time = ctx.session.userdata.parsed_reminder_time
            elif isinstance(ctx.session.userdata, dict):
                parsed_time = ctx.session.userdata.get("parsed_reminder_time")

            original_desc = ""
            if parsed_time and parsed_time.get("original_desc") != time_description:
                original_desc = (
                    f" (from your request: '{parsed_time.get('original_desc')}')"
                )

            confirmation_msg = f"OK. Reminder set for '{reminder_text}' at {trigger_time_dt.strftime('%I:%M %p on %b %d')}{original_desc}."

            if final_is_recurring and final_recurrence_rule:
                if "DAILY" in final_recurrence_rule:
                    confirmation_msg += " It will repeat daily."
                elif "WEEKLY" in final_recurrence_rule:
                    confirmation_msg += " It will repeat weekly."
            return confirmation_msg
        else:
            raise ToolError(
                "Sorry, I couldn't save the reminder due to a database issue."
            )

    except ToolError as te:
        raise te
    except Exception as e:
        logger.error(f"Error setting reminder: {e}", exc_info=True)
        raise ToolError(
            f"Sorry, there was an unexpected error setting the reminder: {str(e)}"
        )


@function_tool
async def list_all_reminders(
    ctx: UserDataContext,
    active_only: bool = True,
) -> str:
    """
    Lists all reminders for the user, optionally filtering for active ones.
    This is a convenience function that calls find_reminder without specific text or type queries.

    IMPORTANT USAGE GUIDELINES:
    1. Use this function when the user wants to see all their reminders without
       specifying any particular search criteria.
    2. By default, only future/active reminders will be shown.
    3. The reminders will be displayed with their text, type, and scheduled time.
    4. Times will be shown in the user's local timezone.
    5. The results can be used for further operations like updating or deleting reminders.

    NOTE: Unlike appointments, reminders do NOT have a notify_before parameter.
    Do NOT ask users about notification time for reminders.

    Args:
        ctx: The run context.
        active_only: (Optional, default True) Whether to list only active/upcoming reminders.

    Returns:
        A string listing all relevant reminders or a message if none are found.
    """
    user_state = ctx.session.userdata.user_state  # Get user_state for logging user_id
    if not user_state or not user_state.user_id:
        logger.warning("list_all_reminders called without a valid user_id in session.")
        # Depending on how strict you want to be, you could raise a ToolError here
        # or proceed and let find_reminder handle the missing user_id.
        # For now, let find_reminder handle it as it already has that check.
    else:
        logger.info(
            f"Listing all reminders for user {user_state.user_id}, ActiveOnly: {active_only}"
        )

    # CORRECTED: Use the new parameter names for find_reminder
    return await find_reminder(
        ctx=ctx,
        reminder_text_query=None,  # Changed from reminder_text
        reminder_type_query=None,  # Changed from reminder_type
        date_query=None,  # Added, as find_reminder now accepts it
        time_query=None,  # Added, as find_reminder now accepts it
        # active_only=active_only,  # This was correct
    )


@function_tool
async def update_existing_reminder(
    ctx: UserDataContext,
    reminder_identifier: str,  # User's reference: "number 6", "the last one", "Play badminton", or "ID:...xxxx"
    new_text: Optional[str] = None,
    new_time_description: Optional[str] = None,
    new_is_recurring: Optional[bool] = None,
    new_recurrence_rule: Optional[str] = None,
    new_description: Optional[str] = None,
) -> str:
    """
    Updates an existing reminder for the user.

    The LLM should first use `find_reminder` to help the user identify the reminder
    if the context is unclear or multiple matches are possible. This tool then uses
    `reminder_identifier` (which could be an index from the search results,
    a snippet of the reminder text, or a partial ID) to pinpoint the reminder
    and apply the specified changes.

    All time-related updates ensure the new time is in the future, unless it's
    a recurring reminder being adjusted.

    IMPORTANT TIME HANDLING GUIDELINES:
    1. If the new_time_description is unclear or ambiguous, ASK the user for clarification
       before calling this function. Request a specific format like "4pm today",
       "every day at 8am", or "Monday at 3pm".
    2. For past times on the current day, the system will reject non-recurring reminders.
       Instead, suggest setting it for tomorrow or a future date.
    3. For recurring reminders with times that would be in the past for the current day,
       the system will automatically set them for the next occurrence.
    4. Always validate that dates are in the future before calling this function.
    5. Use ISO 8601 format for explicit dates (e.g., "2025-04-23T08:30:00").

    NOTE: Unlike appointments, reminders do NOT have a notify_before parameter.
    Do NOT ask users about notification time for reminders.

    Args:
        ctx: The user data context.
        reminder_identifier: A string to identify the reminder. This can be:
            - An index (e.g., "1", "number 2", "the first one") from results of a previous `find_reminder` call.
            - A snippet of the reminder's text.
            - A partial or full ID of the reminder (e.g., "...xxxx").
        new_text: (Optional) The new text for the reminder.
        new_time_description: (Optional) The new time for the reminder (e.g., "tomorrow at 10 AM").
        new_is_recurring: (Optional) Whether the reminder should now be recurring.
        new_recurrence_rule: (Optional) The new recurrence rule string (e.g., "FREQ=DAILY").
        new_description: (Optional) The new description for the reminder.

    Returns:
        A string confirming the update, or an error message if the reminder
        couldn't be found or the update failed.
    """
    user_state = ctx.session.userdata.user_state
    if not user_state.user_id:
        raise ToolError("User ID not available for updating reminders.")

    logger.info(
        f"Attempting to update reminder by '{reminder_identifier}' for user {user_state.user_id}. "
        f"New text: '{new_text}', New time: '{new_time_description}'"
    )

    reminder_to_update_id: Optional[str] = None
    original_reminder_data: Optional[Dict[str, Any]] = None

    # Get previously stored search results from `find_reminder`.
    # These results should reflect the "future-only" default of find_reminder.
    search_results = ctx.session.userdata.current_reminder_search_results
    logger.debug(
        f"Current reminder search results in session data for update: {len(search_results) if search_results else 'None'}"
    )

    identifier_lower = reminder_identifier.lower().strip()

    # --- Step 1: Identify the reminder to update ---
    # The goal is to uniquely identify the reminder the user wants to update.
    # This process allows finding past reminders if the user is referring to one by its ID or text,
    # as updates might be for reminders that are no longer "active" in the default find_reminder sense.
    # This aligns with the requirement: "these case also work into update reminder with finding the id for update".
    # Priority for identification:
    # 1. Index from `current_reminder_search_results` (if a `find_reminder` was just run).
    # 2. Direct ID match (full or partial, e.g., "...xxxx" or hex string).
    # 3. Text-based search (this search queries all reminders, including past ones).

    # 1a. Try to identify by index (number, "first", "last") from previous `find_reminder` results.
    # These `search_results` would typically be for active/future reminders if `find_reminder` was used generally.
    if search_results:
        parsed_idx: Optional[int] = None
        if identifier_lower == "last one" or identifier_lower == "the last one":
            parsed_idx = len(search_results) - 1
        elif identifier_lower.startswith("number "):
            num_part = identifier_lower.split("number ")[1]
            if num_part.isdigit():
                parsed_idx = int(num_part) - 1
        elif identifier_lower.endswith(("st", "nd", "rd", "th")):  # e.g. "1st", "2nd"
            num_part = "".join(filter(str.isdigit, identifier_lower))
            if num_part.isdigit():
                parsed_idx = int(num_part) - 1
        elif identifier_lower.isdigit():  # e.g. "1"
            parsed_idx = int(identifier_lower) - 1

        ordinal_map = {
            "first": 0,
            "second": 1,
            "third": 2,
            "fourth": 3,
            "fifth": 4,
            "one": 0,
            "two": 1,
            "three": 2,
            "four": 3,
            "five": 4,
        }
        cleaned_identifier_for_ordinal = identifier_lower
        if cleaned_identifier_for_ordinal.startswith("the "):
            cleaned_identifier_for_ordinal = cleaned_identifier_for_ordinal[4:]
        if parsed_idx is None and cleaned_identifier_for_ordinal in ordinal_map:
            parsed_idx = ordinal_map[cleaned_identifier_for_ordinal]

        if parsed_idx is not None:
            if 0 <= parsed_idx < len(search_results):
                original_reminder_data = search_results[parsed_idx]
                reminder_to_update_id = str(
                    original_reminder_data.get("_id")
                    or original_reminder_data.get("id")
                )
                logger.info(
                    f"Identified reminder by index {parsed_idx+1} ('{reminder_identifier}') from prior search: ID {reminder_to_update_id} - '{original_reminder_data.get('reminder_text')}'"
                )
            else:
                logger.warning(
                    f"Index {parsed_idx+1} from '{reminder_identifier}' out of bounds for current search results (len {len(search_results)})."
                )
                # Proceed to other identification methods.

    # 1b. If not found by index from prior search results, try to identify by ID.
    # This allows updating reminders (including past ones) if the user provides an ID.
    if not reminder_to_update_id:
        # Check for "...xxxx" format
        if reminder_identifier.startswith("...") and len(reminder_identifier) >= 5:
            id_suffix = reminder_identifier[3:]
            logger.info(f"Attempting to find reminder by ID suffix: '{id_suffix}'.")
            # This requires fetching reminders again if not in current_search_results or if it's a direct ID.
            # The API should support fetching by ID directly if possible, or we search all.
            # Crucially, for updates, we might need to find past reminders if the ID refers to one.
            # So, `active_only=False` might be needed here for ID-based lookup.
            # Efficient ID suffix search: Try to fetch by full ID if possible, else use paginated search
            # Try direct get_reminder for full ID match
            try:
                reminder = await reminder_api_service.get_reminder(id_suffix)
                if reminder and str(reminder.get("_id", "")).endswith(id_suffix):
                    original_reminder_data = reminder
                    reminder_to_update_id = str(reminder.get("_id"))
                    logger.info(
                        f"Identified reminder by ID suffix: {reminder_to_update_id}"
                    )
                else:
                    # If not found, fallback to a paginated search using get_reminders with limit
                    reminders_page = await reminder_api_service.get_reminders(
                        user_id=user_state.user_id,
                        skip=0,
                        limit=20,
                    )
                    matching_reminders = [
                        r
                        for r in reminders_page
                        if str(r.get("_id", "")).endswith(id_suffix)
                    ]
                    if len(matching_reminders) == 1:
                        original_reminder_data = matching_reminders[0]
                        reminder_to_update_id = str(original_reminder_data.get("_id"))
                        logger.info(
                            f"Identified reminder by ID suffix in paginated search: {reminder_to_update_id}"
                        )
                    elif len(matching_reminders) > 1:
                        raise ToolError(
                            f"Multiple reminders found with ID suffix '{id_suffix}'. Please be more specific."
                        )
                    # else: No match by suffix, proceed
            except Exception as e:
                logger.warning(f"Error during ID suffix search: {e}")
            # else: No match by suffix, proceed

        # Check for hex ID format (e.g. a full ObjectId string or a significant part of it)
        elif (
            re.match(r"^[0-9a-f]{12,}$", identifier_lower)
            and len(identifier_lower) >= 12
        ):  # Heuristic for longer ID
            logger.info(
                f"Attempting to find reminder by potential full/partial ID: '{identifier_lower}'."
            )
            try:
                # Efficient ID partial match: Use paginated get_reminders with filter
                reminders_page = await reminder_api_service.get_reminders(
                    user_id=user_state.user_id,
                    skip=0,
                    limit=20,
                )
                matching_reminders = [
                    r
                    for r in reminders_page
                    if identifier_lower in str(r.get("_id", "")).lower()
                ]
                if len(matching_reminders) == 1:
                    original_reminder_data = matching_reminders[0]
                    reminder_to_update_id = str(original_reminder_data.get("_id"))
                    logger.info(
                        f"Identified reminder by ID match: {reminder_to_update_id}"
                    )
                elif len(matching_reminders) > 1:
                    raise ToolError(
                        f"Multiple reminders found matching ID snippet '{identifier_lower}'. Please be more specific."
                    )
                # else: No match by this ID pattern, proceed
            except Exception as e_id_search:
                logger.warning(
                    f"Error during ID-based search for '{identifier_lower}': {e_id_search}"
                )

    # 1c. If not found by index from prior search or by direct ID, try a text-based search.
    # This search is crucial for allowing updates to reminders based on their content.
    # Importantly, it searches *all* reminders (active_only=False) because the user
    # might be referring to a past reminder they wish to modify (e.g., change its text, or reschedule it).
    if not reminder_to_update_id:
        logger.info(
            f"No index or ID match for '{reminder_identifier}'. Searching by text (including non-active)."
        )
        # `search_reminder_with_text` should have an `active_only` flag or similar.
        # For updates, it's safer to search `active_only=False` as user might refer to a past one.
        found_reminders = await reminder_api_service.search_reminder_with_text(
            user_id=user_state.user_id,
            reminder_text=reminder_identifier,  # Use original identifier for text search
            active_only=True,  # Allow finding past reminders for update
        )

        if not found_reminders:
            raise ToolError(
                f"I couldn't find any reminder matching '{reminder_identifier}' to update. "
                "Could you be more specific, or try listing your reminders again?"
            )
        if len(found_reminders) > 1:
            # If multiple found by text, LLM needs to clarify. Store results for it.
            ctx.session.userdata.current_reminder_search_results = found_reminders
            options_str = ", ".join(
                [
                    f"'{r.get('reminder_text', 'Untitled')} (Ref: ...{str(r.get('_id', ''))[-4:]})'"
                    for r in found_reminders[:3]  # Show first few
                ]
            )
            # This error message guides the LLM to ask for clarification.
            raise ToolError(
                f"I found multiple reminders like '{reminder_identifier}': {options_str}. "
                "Which one do you mean? Please say its number from this list, or its full unique text, or its reference ID."
            )

        original_reminder_data = found_reminders[0]
        reminder_to_update_id = str(
            original_reminder_data.get("_id") or original_reminder_data.get("id")
        )
        logger.info(
            f"Identified reminder by text search for '{reminder_identifier}': ID {reminder_to_update_id} - '{original_reminder_data.get('reminder_text')}'"
        )

    if not reminder_to_update_id or not original_reminder_data:
        # Should have been caught by earlier checks, but as a safeguard.
        raise ToolError(
            f"I'm sorry, I couldn't uniquely identify the reminder based on '{reminder_identifier}'. "
            "Please try finding it first or use more specific text or its reference ID."
        )

    # check if reminder type is appointment
    if original_reminder_data.get("reminder_type") == "appointment":
        raise ToolError(
            "You are trying to update an appointment reminder. Please update the appointment instead."
        )

    # --- Step 2: Prepare update payload ---
    update_payload: Dict[str, Any] = {}

    # Get the timezone object from the user's timezone string
    user_timezone = get_user_timezone(user_state)
    logger.info(f"update_existing_reminder: Using timezone: {user_timezone}")

    # Get current time in UTC for comparison
    now_utc_for_check = datetime.now(
        timezone.utc
    )  # Compare against current UTC for past checks

    if new_text is not None:
        update_payload["reminder_text"] = new_text
        # Optionally, re-evaluate reminder_type if text changes significantly (e.g., becomes medication)
        med_keywords = [
            "med",
            "pill",
            "take my",
            "medicine",
            "medication",
            "vitamin",
            "supplement",
        ]  # etc.
        if any(k in new_text.lower() for k in med_keywords):
            update_payload["reminder_type"] = "medication"
        elif (
            "reminder_type" in original_reminder_data
            and original_reminder_data["reminder_type"] == "medication"
        ):
            # If it was medication and new text doesn't imply it, maybe change type or ask LLM to confirm.
            # For now, let's assume it stays general if not explicitly medication.
            update_payload["reminder_type"] = "general"

    new_trigger_dt_for_update: Optional[datetime] = None
    if new_time_description is not None:
        try:
            # Convert local time to UTC using our utility function
            new_trigger_dt_for_update, parsed_is_recurring, parsed_recurrence_rule = (
                convert_local_to_utc(
                    new_time_description,
                    user_timezone,
                    new_is_recurring,
                    new_recurrence_rule,
                )
            )

            # Store the parsed time in the session for reference
            if new_trigger_dt_for_update:
                start_time_local = new_trigger_dt_for_update.astimezone(user_timezone)
                ctx.session.userdata.parsed_update_time = {
                    "utc": new_trigger_dt_for_update,
                    "local": start_time_local,
                    "formatted": start_time_local.strftime("%I:%M %p on %b %d"),
                    "original_desc": new_time_description,
                }
                # Log the parsed time for debugging
                logger.info(
                    f"Parsed update time '{new_time_description}' to {start_time_local.strftime('%I:%M %p on %b %d')}"
                )
            else:
                raise ValueError(
                    f"Could not parse a valid datetime from '{new_time_description}'."
                )

            # Validate the new trigger time for the update.
            # - For non-recurring reminders, the new time MUST be in the future.
            # - For recurring reminders, if the new base time is in the past,
            #   it will be adjusted to the *next valid future occurrence* based on its rule.
            # This ensures reminders are always set for or adjusted to a future point.
            # First, determine the effective recurrence status for this check.
            is_actually_recurring = (
                (new_is_recurring is True)
                or (new_is_recurring is None and parsed_is_recurring)
                or (
                    new_is_recurring is None
                    and original_reminder_data.get("is_recurring")
                )
                or bool(
                    new_recurrence_rule
                    or parsed_recurrence_rule
                    or original_reminder_data.get("recurrence_rule")
                )
            )

            if new_trigger_dt_for_update <= now_utc_for_check:
                if not is_actually_recurring:
                    raise ToolError(
                        f"The new time '{new_time_description}' (parsed as {new_trigger_dt_for_update.strftime('%I:%M %p %Z on %b %d')}) "
                        "is in the past for a non-recurring reminder. Please provide a future time."
                    )
                else:  # It's recurring and new time is past, adjust it
                    logger.info(
                        f"Recurring reminder update time {new_trigger_dt_for_update} is in the past. "
                        "Attempting to adjust to the next valid occurrence."
                    )
                    # This adjustment uses the effective recurrence rule (new or original)
                    # to find the first occurrence of the reminder that is after the current moment.
                    effective_rrule_str = (
                        new_recurrence_rule
                        or parsed_recurrence_rule
                        or original_reminder_data.get("recurrence_rule")
                    )
                    if effective_rrule_str:
                        try:
                            # Ensure dtstart for rrulestr is naive; use original trigger time if available and closer
                            dtstart_for_rrule = new_trigger_dt_for_update.replace(
                                tzinfo=None
                            )
                            if "trigger_time" in original_reminder_data:
                                original_trigger_dt = datetime.fromisoformat(
                                    original_reminder_data["trigger_time"].replace(
                                        "Z", "+00:00"
                                    )
                                ).replace(tzinfo=None)
                                if (
                                    original_trigger_dt > dtstart_for_rrule
                                    and original_trigger_dt
                                    < now_utc_for_check.replace(tzinfo=None)
                                ):  # if original is later but still past
                                    dtstart_for_rrule = original_trigger_dt

                            rrule_obj = dateutil_rrulestr(
                                effective_rrule_str, dtstart=dtstart_for_rrule
                            )
                            next_occurrence_naive = rrule_obj.after(
                                now_utc_for_check.replace(tzinfo=None)
                            )
                            if next_occurrence_naive:
                                new_trigger_dt_for_update = (
                                    next_occurrence_naive.replace(tzinfo=timezone.utc)
                                )
                                logger.info(
                                    f"Adjusted recurring time using RRULE to: {new_trigger_dt_for_update}"
                                )
                            else:  # Should not happen if RRULE is valid and not exhausted
                                raise ValueError(
                                    "RRULE could not find next occurrence after current time."
                                )
                        except Exception as rrule_e:
                            logger.error(
                                f"Error adjusting recurring time with RRULE '{effective_rrule_str}': {rrule_e}. Falling back to simpler adjustment."
                            )
                            raise ToolError(
                                f"Could not adjust recurring time '{new_time_description}' to a future date using its rule: {rrule_e}. Please specify a clear future time."
                            )
                    else:  # No RRULE, simple adjustment (less ideal)
                        logger.warning(
                            f"Recurring reminder {reminder_to_update_id} has no RRULE for adjustment. Attempting basic increment."
                        )
                        new_trigger_dt_for_update_adjusted = new_trigger_dt_for_update
                        while (
                            new_trigger_dt_for_update_adjusted <= now_utc_for_check
                        ):  # Increment until future
                            new_trigger_dt_for_update_adjusted += timedelta(
                                days=1
                            )  # Basic increment, assumes daily or similar
                        if (
                            new_trigger_dt_for_update_adjusted
                            == new_trigger_dt_for_update
                        ):  # No change means it was already future after all
                            pass  # Or if it means it couldn't be adjusted, error
                        new_trigger_dt_for_update = new_trigger_dt_for_update_adjusted
                        if (
                            new_trigger_dt_for_update <= now_utc_for_check
                        ):  # If still in past
                            raise ToolError(
                                f"Could not adjust recurring time '{new_time_description}' to a future date. Please specify a clearer future time."
                            )
                        logger.warning(
                            f"Adjusted recurring time without RRULE (basic increment) to: {new_trigger_dt_for_update}"
                        )

            # --- APPOINTMENT REMINDER VALIDATION (using only reminder data) ---
            # if (
            #     original_reminder_data.get("reminder_type") == "appointment"
            #     and original_reminder_data.get("notify_time") is not None
            #     and original_reminder_data.get("trigger_time") is not None
            # ):
            # try:
            #     reminder_trigger_time = datetime.fromisoformat(
            #         original_reminder_data["trigger_time"].replace("Z", "+00:00")
            #     )

            #     if "notify_time" in original_reminder_data:
            #         notify_time_min = original_reminder_data["notify_time"]
            #     else:
            #         notify_time_min = 0

            #     reminder_deadline = reminder_trigger_time + timedelta(
            #         minutes=notify_time_min
            #     )
            #     if new_trigger_dt_for_update >= reminder_deadline:
            #         raise ToolError(
            #             f"The new reminder time you provided ({new_trigger_dt_for_update.strftime('%I:%M %p on %b %d, %Y')}) is after the allowed notification window for your appointment. "
            #             f"Reminders for this appointment must be set before {reminder_deadline.strftime('%I:%M %p on %b %d, %Y')} (reminder trigger time plus notify time)."
            #         )
            # except Exception as e:
            #     logger.error(
            #         f"Error validating appointment reminder time: {e}",
            #         exc_info=True,
            #     )
            #     raise ToolError(
            #         "Could not validate reminder time against appointment. Please try again later."
            # )

            update_payload["trigger_time"] = new_trigger_dt_for_update.isoformat()

            # Handle recurrence flags based on new time / explicit flags
            update_payload["is_recurring"] = (
                new_is_recurring
                if new_is_recurring is not None
                else (
                    parsed_is_recurring
                    or original_reminder_data.get("is_recurring", False)
                )
            )
            if update_payload["is_recurring"]:
                update_payload["recurrence_rule"] = (
                    new_recurrence_rule
                    if new_recurrence_rule is not None
                    else (
                        parsed_recurrence_rule
                        or original_reminder_data.get("recurrence_rule")
                    )
                )
            else:  # If explicitly set to not recurring, or inferred as not recurring
                update_payload["recurrence_rule"] = None  # Clear rule

        except (
            ValueError
        ) as e_time:  # Catch parsing errors from parse_reminder or our logic
            raise ToolError(
                f"Invalid new time description '{new_time_description}': {str(e_time)}"
            )
    else:  # Time not changing explicitly, but recurrence flags might
        if new_is_recurring is not None:
            update_payload["is_recurring"] = new_is_recurring
            if not new_is_recurring:  # If set to false
                update_payload["recurrence_rule"] = None  # Clear rule
        if new_recurrence_rule is not None:
            update_payload["recurrence_rule"] = new_recurrence_rule
            # If a rule is provided, ensure is_recurring is true, unless explicitly set to false
            if new_is_recurring is None and not update_payload.get(
                "is_recurring", False
            ):  # Check existing or default
                if not original_reminder_data.get(
                    "is_recurring", False
                ) and not update_payload.get("is_recurring", False):
                    update_payload["is_recurring"] = True

    if new_description is not None:
        update_payload["description"] = new_description

    if not update_payload:
        return "No changes were specified for the reminder. Nothing to update."

    # --- Step 3: Call API to update ---
    try:
        updated_reminder = await reminder_api_service.update_reminder(
            reminder_to_update_id, update_payload
        )
        if not updated_reminder:
            # This case should ideally be an exception from the API service if DB fails.
            raise ToolError(
                "Database update failed for the reminder. The reminder was not updated."
            )

        # Create the structured event_data format
        reminder_id = updated_reminder.get("_id") or updated_reminder.get("id", "")
        reminder_text = updated_reminder.get("reminder_text", "N/A")
        reminder_type_val = updated_reminder.get("reminder_type", "general")
        is_recurring = updated_reminder.get("is_recurring", False)
        recurrence_rule = updated_reminder.get("recurrence_rule")

        event_data = {
            "reminderId": str(reminder_id),
            "scheduledTime": updated_reminder.get("trigger_time"),
            "details": reminder_text,
            "type": reminder_type_val,
            "isRecurring": bool(is_recurring),
            "recurrenceRule": recurrence_rule if is_recurring else None,
        }

        await send_event(
            room=ctx.session.userdata.room,  # Corrected to use ctx.session.room
            action_type=ActionType.UPDATE_REMINDER,
            message=f"Reminder '{reminder_text}' updated.",
            data=event_data,
        )

        ctx.session.userdata.current_reminder_search_results = []  # Clear stale results
        updated_text = updated_reminder.get(
            "reminder_text", original_reminder_data.get("reminder_text", "The reminder")
        )

        # Add information about the updated time if applicable
        time_info = ""
        if new_time_description and hasattr(ctx.session.userdata, "parsed_update_time"):
            parsed_time = ctx.session.userdata.parsed_update_time
            time_info = f" The reminder is now set for {parsed_time.get('formatted')}."

        return f"Reminder '{updated_text}' has been updated successfully.{time_info}"

    except Exception as e:
        logger.error(
            f"Error during API update for reminder ID {reminder_to_update_id}: {e}",
            exc_info=True,
        )
        # Check if it's a ToolError already, if so, re-raise it.
        if isinstance(e, ToolError):
            raise e
        raise ToolError(
            f"Sorry, I couldn't update the reminder due to a system error: {str(e)}"
        )


# --- Delete Reminder Tool ---


@function_tool
async def delete_reminder_tool(
    ctx: UserDataContext,
    reminder_text_query: Optional[str] = None,
    date_query: Optional[str] = None,
    confirm_delete: Optional[bool] = None,
    selection: Optional[str] = None,
) -> str:
    """
    Conversational tool to delete a reminder by text and/or date, with user confirmation and timezone handling.

    Args:
        ctx: User data context.
        reminder_text_query: (Optional) Text of the reminder.
        date_query: (Optional) Date string (e.g., '20th May').
        confirm_delete: (Optional) Whether the user confirmed deletion.
        selection: (Optional) User selection if multiple reminders found.

    Returns:
        Prompt, confirmation, or result message.
    """
    user_state = ctx.session.userdata.user_state
    if not user_state.user_id:
        raise ToolError("User ID needed for deleting reminders.")

    # 1. Ask for text if not provided, else ask for date
    if not reminder_text_query and not date_query:
        return "What is the text of the reminder you want to delete?"
    if not reminder_text_query and date_query:
        return "Please provide the text of the reminder, or say 'no text' to search by date only."

    # 2. Prepare search params (convert date to UTC for API)
    user_timezone = get_user_timezone(user_state)
    now_local = datetime.now(user_timezone)
    from app.utils.time_utils import convert_utc_to_local

    search_params = {
        "user_id": user_state.user_id,
        "skip": 0,
        "limit": 20,
    }
    if reminder_text_query and reminder_text_query.lower() != "no text":
        search_params["reminder_text"] = reminder_text_query

    # Date handling: if date provided, search for that date (00:00 to 23:59, converted to UTC)
    if date_query:
        try:
            parsed_date = dateutil_parse(
                date_query, fuzzy=True, default=now_local
            ).date()
        except Exception:
            return f"Sorry, I couldn't understand the date '{date_query}'. Please provide a clearer date, e.g., '20th May' or 'yesterday'."
        # Start and end of day in user's timezone
        start_dt_local = datetime.combine(parsed_date, datetime.min.time()).replace(
            tzinfo=user_timezone
        )
        end_dt_local = datetime.combine(parsed_date, datetime.max.time()).replace(
            tzinfo=user_timezone
        )
        start_dt_utc = start_dt_local.astimezone(timezone.utc)
        end_dt_utc = end_dt_local.astimezone(timezone.utc)
        search_params["start_date"] = start_dt_utc.strftime("%Y-%m-%d")
        search_params["end_date"] = end_dt_utc.strftime("%Y-%m-%d")
        search_params["start_time"] = start_dt_utc.strftime("%H:%M")
        search_params["end_time"] = end_dt_utc.strftime("%H:%M")

    # 3. Search for reminders (if not already in session)
    if (
        not hasattr(ctx.session.userdata, "current_reminder_delete_results")
        or not ctx.session.userdata.current_reminder_delete_results
    ):
        reminders = await reminder_api_service.get_reminders(**search_params)
        if not reminders:
            # Log the search parameters for debugging
            logger.info(f"No reminders found with parameters: {search_params}")

            # Provide a more helpful message based on the search criteria
            if reminder_text_query:
                return f"No reminders found containing '{reminder_text_query}'. Would you like to create a new reminder with this text?"
            elif date_query:
                return f"No reminders found for {date_query}. Would you like to create a new reminder for this date?"
            else:
                return "No reminders found matching your criteria. Would you like to create a new reminder?"
        ctx.session.userdata.current_reminder_delete_results = reminders

        # If multiple, prompt for selection
        if len(reminders) > 1:
            response_lines = [
                "Multiple reminders found. Which one do you want to delete?"
            ]
            for i, r in enumerate(reminders[:5]):
                r_text = r.get("reminder_text", "Untitled Reminder")
                r_time_str = r.get("trigger_time", "No time set")
                try:
                    r_time_dt_utc = datetime.fromisoformat(
                        r_time_str.replace("Z", "+00:00")
                    )
                    r_time_dt_local = convert_utc_to_local(r_time_dt_utc, user_timezone)
                    r_time_disp = r_time_dt_local.strftime("%I:%M %p on %b %d, %Y")
                except Exception:
                    r_time_disp = r_time_str
                response_lines.append(f"{i+1}. '{r_text}' at {r_time_disp}")
            return "\n".join(response_lines)
        # If only one, store and proceed to confirmation
        ctx.session.userdata.selected_reminder_to_delete = reminders[0]
    else:
        reminders = ctx.session.userdata.current_reminder_delete_results

    # 4. If multiple, handle user selection
    if len(reminders) > 1 and selection:
        try:
            idx = int(selection) - 1
            if 0 <= idx < len(reminders):
                ctx.session.userdata.selected_reminder_to_delete = reminders[idx]
            else:
                return "Invalid selection. Please provide a valid number."
        except Exception:
            return "Invalid selection. Please provide a valid number."

    # 5. Confirm before delete
    reminder = ctx.session.userdata.selected_reminder_to_delete
    reminder_text = reminder.get("reminder_text", "Untitled Reminder")
    if confirm_delete is None:
        return (
            f"Are you sure you want to delete the reminder '{reminder_text}'? (yes/no)"
        )

    # 6. If confirmed, delete
    if confirm_delete:
        reminder_id = str(reminder.get("_id") or reminder.get("id"))
        deleted = await reminder_api_service.delete_reminder(reminder_id)
        # Clear session variables
        ctx.session.userdata.current_reminder_delete_results = []
        ctx.session.userdata.selected_reminder_to_delete = None
        if deleted:
            return f"Reminder '{reminder_text}' has been deleted."
        else:
            return f"Failed to delete reminder '{reminder_text}'."
    else:
        # User said no
        ctx.session.userdata.current_reminder_delete_results = []
        ctx.session.userdata.selected_reminder_to_delete = None
        return "Deletion cancelled."
