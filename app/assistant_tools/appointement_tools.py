# app/assistant_tools/appointement_tools.py (Revised based on API Service)
import asyncio
import json
import re
from datetime import date, datetime, timedelta, timezone
from typing import Any, Dict, List, Optional, Union

from app.core.logger_setup import logger

# Use zoneinfo for modern timezone handling if available (Python 3.9+)
try:
    from zoneinfo import ZoneInfo, ZoneInfoNotFoundError
except ImportError:
    try:
        import pytz
        from pytz import UnknownTimeZoneError as ZoneInfoNotFoundError  # Alias error

        def ZoneInfo(key):
            return pytz.timezone(key)

    except ImportError:
        print("Missing timezone library. Please install 'tzdata' or 'pytz'.")

        class ZoneInfoNotFoundError(Exception):
            pass

        def ZoneInfo(key):
            raise ZoneInfoNotFoundError(
                f"Timezone library not installed for key: {key}"
            )


from dateutil.parser import parse as dateutil_parse
from dateutil.rrule import DAILY, MONTHLY, WEEKLY, YEARLY, rrule
from dateutil.rrule import rrulestr as dateutil_rrulestr
from livekit.agents import ToolError, function_tool

# Import your application modules
from app.constants.action_types import ActionStatus, ActionType
from app.constants.env_constants import DEFAULT_APPOINTMENT_NOTIFY_BEFORE

# Import API service functions
from app.services import (
    appointment_api_service,
    contact_api_service,
    reminder_api_service,
)

# Import SessionData and UserDataContext
from app.state.session_data import UserDataContext

# --- HELPER FUNCTIONS ---


def get_user_state_from_context(ctx: UserDataContext):
    """
    Helper function to get user state from context with robust access pattern.
    Handles both attribute and dictionary access patterns.
    """
    if not ctx.session or not ctx.session.userdata:
        raise ToolError("Internal session error.")

    # Get user state with robust access pattern
    user_state = None
    if hasattr(ctx.session.userdata, "user_state"):
        user_state = ctx.session.userdata.user_state
    elif (
        isinstance(ctx.session.userdata, dict)
        and "session_data" in ctx.session.userdata
    ):
        user_state = ctx.session.userdata["session_data"].user_state
    else:
        raise ToolError("Unable to access user state from session data.")

    if not user_state:
        raise ToolError("User state not available.")

    return user_state


# Import utilities
from app.utils.event_send import send_event
from app.utils.reminder_time import parse_reminder  # Ensure this is robust
from app.utils.time_utils import (  # Import our utility functions
    convert_local_to_utc,
    get_user_timezone,
)

# Import utility functions
from app.utils.transform_data import extract_contact_details

# --- Appointment Tools ---


def _resolve_identifier_from_search_results(
    search_results: List[Dict[str, Any]],
    identifier: str,
    user_timezone: ZoneInfo,  # For time-based matching
    last_date_query: Optional[str] = None,  # For context if identifier is just a time
) -> Optional[Dict[str, Any]]:
    """
    Resolves an appointment from a list of search results based on an identifier.
    Identifier can be an index (1-based), Ref ID (...xxxx), title, or time.
    """
    if not search_results or not identifier:
        return None

    identifier_lower = identifier.lower().strip()
    resolved_appt = None

    # Try index
    parsed_idx = None
    try:
        if identifier_lower.startswith("number "):
            parsed_idx = int(identifier_lower.split("number ")[1]) - 1
        elif identifier_lower.isdigit():  # e.g. "1", "2"
            parsed_idx = int(identifier_lower) - 1
    except (ValueError, IndexError):
        pass  # Not a valid index string

    if parsed_idx is not None and 0 <= parsed_idx < len(search_results):
        resolved_appt = search_results[parsed_idx]
        logger.info(
            f"Helper: Identified by index {parsed_idx+1} from search results: {resolved_appt.get('title')}"
        )
        return resolved_appt

    # Try Ref ID (e.g., "...a1b2")
    id_match = re.match(r"^(?:.*)(\.{3})([0-9a-f]{4,})$", identifier_lower)
    if id_match:
        id_suffix = id_match.group(2).lower()
        for appt in search_results:
            if str(appt.get("_id", "")).lower().endswith(id_suffix):
                resolved_appt = appt
                logger.info(
                    f"Helper: Identified by Ref ID '...{id_suffix}' from search results: {resolved_appt.get('title')}"
                )
                return resolved_appt
        # If we reach here, a Ref ID-like pattern was given but didn't match any in the list

    # Try exact title match
    for appt in search_results:
        if identifier_lower == appt.get("title", "").lower():
            resolved_appt = appt
            logger.info(
                f"Helper: Identified by exact title '{identifier_lower}' from search results: {resolved_appt.get('title')}"
            )
            return resolved_appt

    # Try partial title match (if no exact match) - be cautious with this, can be ambiguous
    # For now, let's skip overly broad partial title matches in this helper to avoid ambiguity.
    # The LLM should guide the user to be more specific or use the number if titles are similar.

    # Try time match (more complex, needs context like last_date_query)
    # This part would require careful parsing and comparison against appt['start_time']
    # For brevity, this is simplified. A full time match would convert appt['start_time']
    # (which might be user-friendly string or ISO) to a comparable datetime object.
    try:
        now_local_for_parsing = datetime.now(user_timezone)
        default_date_for_parsing = now_local_for_parsing
        if last_date_query:
            # Attempt to parse last_date_query to provide date context for time-only identifier
            parsed_context_date = parse_relative_date(
                last_date_query, now_local_for_parsing
            )
            if parsed_context_date:
                default_date_for_parsing = datetime.combine(
                    parsed_context_date, datetime.min.time(), tzinfo=user_timezone
                )

        parsed_user_time = dateutil_parse(
            identifier, fuzzy=True, default=default_date_for_parsing
        )
        parsed_user_time = (
            parsed_user_time.astimezone(user_timezone)
            if parsed_user_time.tzinfo
            else parsed_user_time.replace(tzinfo=user_timezone)
        )

        matching_time_appts = []
        for appt in search_results:
            appt_start_time_str = appt.get(
                "start_time"
            )  # This might be user-friendly or ISO
            if appt_start_time_str:
                try:
                    # Attempt to parse the appointment's start time string
                    # This needs to be robust to handle various formats find_appointment might have stored
                    appt_dt_local = dateutil_parse(
                        appt_start_time_str, fuzzy=True
                    ).astimezone(user_timezone)

                    if (
                        appt_dt_local.hour == parsed_user_time.hour
                        and appt_dt_local.minute == parsed_user_time.minute
                        and (
                            not last_date_query
                            or appt_dt_local.date() == parsed_user_time.date()
                        )
                    ):  # Match date if context was given
                        matching_time_appts.append(appt)
                except Exception:
                    continue  # Cannot parse this appt's time

        if len(matching_time_appts) == 1:
            resolved_appt = matching_time_appts[0]
            logger.info(
                f"Helper: Identified by time '{identifier}' from search results: {resolved_appt.get('title')}"
            )
            return resolved_appt
        elif len(matching_time_appts) > 1:
            logger.info(
                f"Helper: Multiple appointments match time '{identifier}'. Ambiguous."
            )
            # Do not return, let higher level function handle ambiguity
            return None  # Or raise a specific AmbiguousTimeMatchError

    except Exception as e_time:
        logger.warning(
            f"Helper: Could not parse '{identifier}' as a time or error during time match: {e_time}"
        )

    logger.info(
        f"Helper: Could not resolve identifier '{identifier}' from search results."
    )
    return None


@function_tool
async def handle_appointment_update_flow(
    ctx: UserDataContext,
    update_type: Optional[str] = None,
) -> str:
    """
    Initiates the appointment update or find process by determining the user's intent.
    Called when the user expresses a desire to 'update' or 'find' an appointment, BEFORE searching.

    Behavior:
    - If no update_type is provided (e.g., user says "update my appointment"), prompts for what to update/find.
    - If update_type is provided, confirms intent and guides user to provide appointment details.
    - Sets search intent for `find_appointment`:
      - "update_notes": For notes/summary, includes past appointments (`active_only=False`).
      - "update_scheduling": For time/date/title, focuses on future appointments (`active_only=True`).

    Args:
        ctx: User data context.
        update_type: (Optional) Type of update (e.g., "notes", "time", "date", "title").

    Returns:
        Prompt for update type or guidance to provide appointment details.
    """
    if (
        not ctx.session
        or not ctx.session.userdata
        or not ctx.session.userdata.user_state
    ):
        raise ToolError("Internal session error.")

    if not update_type:
        logger.info("handle_appointment_update_flow: Asking user for update type.")
        return (
            "Okay, what specifically do you want to update or check about the appointment? "
            "For example, are you adding notes or summary, or changing scheduling details like the time, date, or title?"
        )
    else:
        update_type_lower = update_type.lower().strip()
        if update_type_lower in [
            "notes",
            "summary",
            "description",
            "details",
            "add notes",
            "add summary",
            "update notes",
        ]:
            search_intent = "update_notes"
            case_desc = "notes or summary (this allows looking at past appointments)"
            logger.info(
                f"handle_appointment_update_flow: Determined intent '{search_intent}' for update type '{update_type}'."
            )
            return (
                f"Okay, you want to update the {case_desc}. "
                f"Please tell me the title or approximate date/time of the appointment you're looking for."
            )
        else:
            search_intent = "update_scheduling"
            case_desc = "scheduling details (like time, date, title - focusing on upcoming appointments)"
            logger.info(
                f"handle_appointment_update_flow: Determined intent '{search_intent}' for update type '{update_type}'."
            )
            return (
                f"Got it, you want to update {case_desc}. "
                f"Please tell me the title or approximate date/time of the appointment you want to change."
            )


def parse_relative_date(date_query: str, reference_date: datetime) -> Optional[date]:
    """
    Parses a date expression (relative or absolute) into a date object.

    Supports a wide range of date formats, including relative dates (e.g., "today", "yesterday", "last week", "in 2 days")
    and absolute dates (e.g., "2023-10-09", "May 10, 2025", "10/09/2023", "10th May"). Uses reference_date
    (in user's timezone, e.g., Asia/Kolkata) as the basis for relative calculations. Handles past and future dates
    without restrictions.

    Args:
        date_query: String containing the date (e.g., "today", "2023-10-09", "May 10th").
        reference_date: Datetime in user's timezone to compute relative dates.

    Returns:
        date: Parsed date (e.g., `2025-05-15` for "today" on 2025-05-15).
        None: If the date_query cannot be parsed.

    Supported Formats:
        - Relative: "today", "yesterday", "tomorrow", "last week", "next month", "3 days ago", "in 2 weeks".
        - Absolute: "2023-10-09", "10/09/2023", "09/10/2023", "May 10, 2025", "10th May", "May 10th".
    """
    if not date_query or not isinstance(date_query, str):
        return None

    date_query_lower = date_query.lower().strip()
    try:
        # Handle relative date expressions
        if date_query_lower in ["today", "now", "this day"]:
            return reference_date.date()
        elif date_query_lower in ["yesterday", "day before", "previous day"]:
            return (reference_date - timedelta(days=1)).date()
        elif date_query_lower in [
            "before yesterday",
            "two days ago",
            "day before yesterday",
        ]:
            return (reference_date - timedelta(days=2)).date()
        elif date_query_lower in ["tomorrow", "next day"]:
            return (reference_date + timedelta(days=1)).date()
        elif date_query_lower in ["day after tomorrow", "two days from now"]:
            return (reference_date + timedelta(days=2)).date()
        elif date_query_lower in ["last week", "previous week"]:
            return (reference_date - timedelta(days=7)).date()
        elif date_query_lower in ["next week"]:
            return (reference_date + timedelta(days=7)).date()
        elif date_query_lower in ["last month", "previous month"]:
            return (reference_date - timedelta(days=30)).date()
        elif date_query_lower in ["next month"]:
            return (reference_date + timedelta(days=30)).date()

        # Handle "X days/weeks/months ago" or "in X days/weeks/months"
        days_ago_match = re.match(r"(\d+)\s*days?\s*ago", date_query_lower)
        if days_ago_match:
            days = int(days_ago_match.group(1))
            return (reference_date - timedelta(days=days)).date()

        weeks_ago_match = re.match(r"(\d+)\s*weeks?\s*ago", date_query_lower)
        if weeks_ago_match:
            weeks = int(weeks_ago_match.group(1))
            return (reference_date - timedelta(weeks=weeks)).date()

        months_ago_match = re.match(r"(\d+)\s*months?\s*ago", date_query_lower)
        if months_ago_match:
            months = int(months_ago_match.group(1))
            return (reference_date - timedelta(days=months * 30)).date()

        in_days_match = re.match(r"in\s*(\d+)\s*days?", date_query_lower)
        if in_days_match:
            days = int(in_days_match.group(1))
            return (reference_date + timedelta(days=days)).date()

        in_weeks_match = re.match(r"in\s*(\d+)\s*weeks?", date_query_lower)
        if in_weeks_match:
            weeks = int(in_weeks_match.group(1))
            return (reference_date + timedelta(weeks=weeks)).date()

        in_months_match = re.match(r"in\s*(\d+)\s*months?", date_query_lower)
        if in_months_match:
            months = int(in_months_match.group(1))
            return (reference_date + timedelta(days=months * 30)).date()

        # Handle absolute date formats with regex
        iso_date_match = re.match(r"(\d{4})-(\d{2})-(\d{2})", date_query)
        if iso_date_match:
            year, month, day = map(int, iso_date_match.groups())
            return date(year, month, day)

        us_date_match = re.match(r"(\d{1,2})/(\d{1,2})/(\d{4})", date_query)
        if us_date_match:
            month, day, year = map(int, us_date_match.groups())
            return date(year, month, day)

        uk_date_match = re.match(r"(\d{1,2})\.(\d{1,2})\.(\d{4})", date_query)
        if uk_date_match:
            day, month, year = map(int, uk_date_match.groups())
            return date(year, month, day)

        # Fallback to dateutil_parse for textual or ambiguous formats
        parsed_dt = dateutil_parse(date_query, fuzzy=True, default=reference_date)
        return parsed_dt.date()

    except (ValueError, AttributeError) as e:
        logger.warning(f"Failed to parse date_query '{date_query}': {e}")
        return None


@function_tool
async def find_appointment(
    ctx: UserDataContext,
    title_query: Optional[str] = None,
    date_query: Optional[str] = None,
    time_query: Optional[str] = None,
    search_intent: Optional[str] = None,
) -> str:
    """
    Searches for appointments based on user-provided details to support 'update' or 'find' requests.

    IMPORTANT VALIDATION GUIDELINES:
    1. Always validate user input before calling this function:
       a. For date queries, ensure they are in a recognizable format (e.g., "May 20th", "tomorrow")
       b. For time queries, ensure they are in a recognizable format (e.g., "3pm", "15:00")
       c. If the input is ambiguous, ask for clarification before calling this function

    2. Handle date-only searches properly:
       a. For date-only searches (like "May 20th"), the function will search the entire day
       b. The search will include appointments from 00:00 to 23:59 in the user's local timezone
       c. The function will convert these times to UTC for the API call

    Behavior:
    - **Case 1**: If no `title_query` or `date_query`, prompts user to provide title or date.
    - **Case 2**: For notes/summary updates (`search_intent="update_notes"`), searches with `title_query` if provided,
      including past appointments (`active_only=False`). Prompts for title if `title_query=None`.
    - **Case 3**: For title updates (`search_intent="update_scheduling"`), prompts for `date_query` if none provided.
      If no date, searches with current date, no `start_time` (implies `00:00`).
    - **Case 4**: Handles relative date/time queries (e.g., "yesterday", "last week", "tomorrow"):
      - Uses `parse_relative_date` to parse `date_query` into a date (e.g., "yesterday" -> `2025-05-14`).
      - Sets `active_only=False` for past dates or `update_notes`.
      - Prompts for title if `title_query=None` and date alone is broad (e.g., "last week").
      - Only includes `start_time` if `time_query` provided.
    - **Upcoming Appointments**: If `search_intent="upcoming"`, searches from current date/time with `active_only=True`.
    - Lists up to 10 appointments in user's timezone, prompting for clarification if multiple matches.
    - Stores results in session for `update_appointment`.

    Search Logic:
    - Uses `title_query` if provided, else prompts for title/date.
    - Parses `date_query` with `parse_relative_date` for relative dates.
    - For title updates without date, defaults to current date.
    - Avoids `start_time` unless `time_query` provided.

    Time Handling:
    - Supports relative dates via `parse_relative_date` (e.g., "yesterday", "3 days ago").
    - Uses Asia/Kolkata timezone for display, UTC for API.
    - Past appointments included for `update_notes` or past dates.

    Args:
        ctx: User data context.
        title_query: (Optional) Text to search in titles.
        date_query: (Optional) Date string (e.g., "yesterday", "2025-05-25").
        time_query: (Optional) Time string (e.g., "3pm").
        search_intent: (Optional) Intent:
                       - "update_notes": Include past appointments.
                       - "update_scheduling": Future appointments.
                       - "upcoming": Search from current time.

    NOTE: Do not mention Ref: ... to users

    Returns:
        List of found appointments, prompt for title/date, or error.
        Stores results in `ctx.session.userdata.current_appointment_search_results`.
    """
    # Handle session data access based on structure
    if not ctx.session or not ctx.session.userdata:
        raise ToolError("Internal session error.")

    # Get user state with robust access pattern
    user_state = None
    if hasattr(ctx.session.userdata, "user_state"):
        user_state = ctx.session.userdata.user_state
    elif (
        isinstance(ctx.session.userdata, dict)
        and "session_data" in ctx.session.userdata
    ):
        user_state = ctx.session.userdata["session_data"].user_state
    else:
        raise ToolError("Unable to access user state from session data.")

    if not user_state or not user_state.user_id:
        raise ToolError("User ID needed for finding appointments.")

    logger.info(
        f"find_appointment: User {user_state.user_id}, Title '{title_query}', "
        f"Date '{date_query}', Time '{time_query}', Intent '{search_intent}'"
    )

    # --- Latency Management ---
    # Create a task that will send a status message if the search takes longer than expected
    from app.utils.latency_management import (
        cleanup_status_task,
        create_status_update_task,
    )

    status_update_task = await create_status_update_task(
        ctx,
        delay=0.04,  # 40 milliseconds - VERY SHORT!
        message="Just a moment while I look that up for you...",
        operation_name="find_appointment",
    )
    # --- End Latency Management Setup ---

    # Clear previous results and store date_query
    # Handle session data storage based on structure
    if hasattr(ctx.session.userdata, "current_appointment_search_results"):
        ctx.session.userdata.current_appointment_search_results = []
    elif isinstance(ctx.session.userdata, dict):
        ctx.session.userdata["current_appointment_search_results"] = []

    if hasattr(ctx.session.userdata, "last_appointment_date_query"):
        ctx.session.userdata.last_appointment_date_query = date_query
    elif isinstance(ctx.session.userdata, dict):
        ctx.session.userdata["last_appointment_date_query"] = date_query
    logger.info(f"Stored date_query: {date_query}")

    # Case 1 & 2: Prompt if no title or date (or title missing for notes update)
    if not title_query and not date_query:
        logger.info("find_appointment: No title or date provided. Prompting user.")
        return (
            "Please provide the title or approximate date of the appointment you're looking for. "
            "For example, 'doctor appointment' or 'yesterday'."
        )

    # If we have a date_query but no title_query, we can proceed with the search
    # This allows users to say "find my yesterday or past appointment" without requiring a title
    if search_intent == "update_notes" and not title_query and date_query:
        # Instead of asking for a title, let's proceed with the date-only search
        logger.info(
            f"find_appointment: Proceeding with date-only search for '{date_query}'"
        )
        # Continue with the search - don't return early

    # Get user's timezone and current time
    user_timezone = get_user_timezone(user_state)
    logger.info(f"Using timezone: {user_timezone}")
    now_utc = datetime.now(timezone.utc)
    now_local = datetime.now(user_timezone)

    # Determine if past query
    is_past_query = False
    parsed_date = None
    if date_query:
        parsed_date = parse_relative_date(date_query, now_local)
        if parsed_date:
            if parsed_date < now_local.date():
                is_past_query = True
        else:
            logger.warning(f"Could not parse date_query '{date_query}'")
            return (
                f"Sorry, I couldn't understand the date '{date_query}'. Please provide a clearer date, "
                "e.g., 'yesterday', 'last week', or '2025-05-25'."
            )

    # Prepare API parameters
    api_params = {
        "user_id": user_state.user_id,
        "limit": 20,
        "active_only": True,
    }
    if title_query:
        api_params["title"] = title_query

    # Set date/time for API call using new time_utils functions
    from app.utils.time_utils import user_time_to_utc_iso, utc_iso_to_user_time

    api_start_date_str = None
    api_end_date_str = None
    api_start_time_str = None
    if parsed_date:
        # For date-only searches (like "May 20th"), include the entire day from 00:00 to 23:59
        # Convert parsed_date (local) to UTC ISO for API, allow today and past dates for search

        # Start of day in user's timezone
        start_dt_local = datetime.combine(parsed_date, datetime.min.time()).replace(
            tzinfo=user_timezone
        )
        # End of day in user's timezone
        end_dt_local = datetime.combine(parsed_date, datetime.max.time()).replace(
            tzinfo=user_timezone
        )

        # Convert to UTC for API
        start_dt_utc = start_dt_local.astimezone(timezone.utc)
        end_dt_utc = end_dt_local.astimezone(timezone.utc)

        # Format for API
        api_start_date_str = start_dt_utc.strftime("%Y-%m-%d")
        api_end_date_str = end_dt_utc.strftime("%Y-%m-%d")

        logger.info(
            f"Date search from {api_start_date_str} to {api_end_date_str} (UTC)"
        )

        if time_query:
            try:
                api_start_time_str = user_time_to_utc_iso(
                    time_query, user_state, allow_past=True
                )[
                    11:16
                ]  # Only time part for API
            except Exception:
                logger.warning(f"Could not parse time_query '{time_query}'")
                return (
                    f"Sorry, I couldn't understand the time '{time_query}'. Please provide a clearer time, "
                    "e.g., '3pm' or '15:00'."
                )
        else:
            # If no time provided, use current time as default for better search results
            # This helps when searching for appointments on a specific date
            current_time = now_local.strftime("%H:%M")
            logger.info(
                f"No time provided, using current time as default: {current_time}"
            )
            try:
                api_start_time_str = user_time_to_utc_iso(
                    current_time, user_state, allow_past=True
                )[
                    11:16
                ]  # Only time part for API
            except Exception as e:
                logger.warning(f"Could not use current time as default: {e}")
                # Continue without time if we can't use current time

    # Case 3: For title updates without date
    if search_intent == "update_scheduling" and not date_query and not time_query:
        logger.info("find_appointment: No date for title update. Prompting for date.")
        return (
            "Please provide the date for the appointment you want to update the title for, "
            "e.g., 'today', 'tomorrow', or '25th May'."
        )

    # For update_notes intent with date but no title, we'll proceed with the search
    # This allows users to say "find my yesterday appointment" without requiring a title

    # Set default date for upcoming or title searches
    if not api_start_date_str:
        if search_intent == "upcoming":
            api_start_date_str = user_time_to_utc_iso(
                now_local.strftime("%Y-%m-%d"), user_state
            )[:10]
            api_start_time_str = (
                user_time_to_utc_iso(now_local.strftime("%H:%M"), user_state)[11:16]
                if time_query
                else None
            )
        elif search_intent == "update_scheduling":
            api_start_date_str = user_time_to_utc_iso(
                now_local.strftime("%Y-%m-%d"), user_state
            )[:10]
            api_start_time_str = None
        elif title_query:
            api_start_date_str = None  # Search without date if title provided

    if api_start_date_str:
        api_params["start_date"] = api_start_date_str
        if api_end_date_str:
            api_params["end_date"] = api_end_date_str
        if api_start_time_str:
            api_params["start_time"] = api_start_time_str

    final_api_params = {k: v for k, v in api_params.items() if v is not None}
    logger.info(f"API call params: {final_api_params}")

    try:
        appointments = await appointment_api_service.get_appointments_by_user(
            **final_api_params
        )
        if not appointments:
            return "No appointments found matching your criteria."
        logger.info(
            f"\n\nFound {len(appointments)} appointments matching criteria., {appointments}\n\n"
        )

        # Convert appointment times from UTC to local for display
        for appt in appointments:
            if "start_time" in appt and appt["start_time"]:
                try:
                    appt["start_time"] = utc_iso_to_user_time(
                        appt["start_time"], user_state
                    ).strftime("%I:%M %p on %b %d, %Y")
                except Exception:
                    pass
            if "end_time" in appt and appt["end_time"]:
                try:
                    appt["end_time"] = utc_iso_to_user_time(
                        appt["end_time"], user_state
                    ).strftime("%I:%M %p on %b %d, %Y")
                except Exception:
                    pass

        # Handle multiple title matches
        if title_query and len(appointments) > 1:
            title_matches = [
                appt
                for appt in appointments
                if appt.get("title", "").lower() == title_query.lower()
            ]
            if len(title_matches) > 1:
                logger.info(f"Multiple appointments found for title '{title_query}'.")
                response_lines = [f"Which '{title_query}' appointment do you mean?"]
                for i, appt in enumerate(title_matches[:10]):
                    time_disp = appt.get("start_time", "No time set")
                    response_lines.append(f"{i+1}. {time_disp}")
                # Handle session data storage based on structure
                if hasattr(ctx.session.userdata, "current_appointment_search_results"):
                    ctx.session.userdata.current_appointment_search_results = (
                        appointments
                    )
                elif isinstance(ctx.session.userdata, dict):
                    ctx.session.userdata["current_appointment_search_results"] = (
                        appointments
                    )
                return "\n".join(response_lines)

        # Store results
        # Handle session data storage based on structure
        if hasattr(ctx.session.userdata, "current_appointment_search_results"):
            ctx.session.userdata.current_appointment_search_results = appointments
        elif isinstance(ctx.session.userdata, dict):
            ctx.session.userdata["current_appointment_search_results"] = appointments
        logger.info(f"Stored {len(appointments)} appointments in session")

        # Format response
        response_lines = [f"Found {len(appointments)} appointment(s):"]
        now_utc = datetime.now(timezone.utc)
        for i, appt in enumerate(appointments):
            appt_id = str(appt.get("_id") or appt.get("id", "UnknownID"))
            id_snip = appt_id[-6:] if len(appt_id) >= 6 else appt_id
            title = appt.get("title", "Untitled")
            time_disp = appt.get("start_time", "No time set")
            is_past = False
            try:
                # Convert back to UTC for comparison
                start_dt_utc = datetime.fromisoformat(
                    appt.get("start_time", "").replace("Z", "+00:00")
                )
                is_past = start_dt_utc < now_utc
            except Exception:
                pass
            if is_past and search_intent != "update_notes":
                time_disp += " (Past)"
            notes_preview = ""
            if search_intent == "update_notes" and appt.get("description"):
                notes = appt.get("description", "")
                preview_len = 40
                notes_preview = f" - Notes: \"{notes[:preview_len]}{'...' if len(notes) > preview_len else ''}\""
            response_lines.append(
                f"{i+1}. '{title}' at {time_disp}. (Ref: ...{id_snip}){notes_preview}"
            )

        # Clean up the status update task before returning
        await cleanup_status_task(status_update_task)
        return "\n".join(response_lines)

    except Exception as e:
        # Clean up the status update task in case of error
        await cleanup_status_task(status_update_task)
        logger.error(f"API error in find_appointment: {e}", exc_info=True)
        raise ToolError("Error finding appointments.")


@function_tool
async def select_appointment_for_update(
    ctx: UserDataContext,
    appointment_identifier: Optional[str] = None,
) -> str:
    """
    Identifies and selects an appointment for a subsequent update operation.
    Manages session state for `selected_appointment_to_update` and `current_appointment_search_results`.
    Prompts for clarification if needed or guides the LLM to use `find_appointment`.

    Args:
        ctx: User data context.
        appointment_identifier: User's input to identify the appointment (e.g., "number 1",
                                title, time, or a query like "trip with parent").

    Returns:
        A string message for the LLM/user to guide the conversation.
    """
    # Handle session data access based on structure
    if not ctx.session or not ctx.session.userdata:
        raise ToolError("Internal session error: User context not available.")

    # Get user state with robust access pattern
    user_state = None
    if hasattr(ctx.session.userdata, "user_state"):
        user_state = ctx.session.userdata.user_state
    elif (
        isinstance(ctx.session.userdata, dict)
        and "session_data" in ctx.session.userdata
    ):
        user_state = ctx.session.userdata["session_data"].user_state
    else:
        raise ToolError("Unable to access user state from session data.")

    if not user_state or not user_state.user_id:
        raise ToolError("User ID not found. Cannot select appointments.")

    logger.info(
        f"select_appointment_for_update called with identifier: '{appointment_identifier}'"
    )
    user_timezone = get_user_timezone(user_state)

    previously_selected_appt = getattr(
        ctx.session.userdata, "selected_appointment_to_update", None
    )
    search_results = getattr(
        ctx.session.userdata, "current_appointment_search_results", []
    )
    last_date_query = getattr(ctx.session.userdata, "last_appointment_date_query", None)

    # Case 1: An appointment was already selected, and no new identifier is given
    if previously_selected_appt and not appointment_identifier:
        title = previously_selected_appt.get(
            "title", "the previously selected appointment"
        )
        # Ensure start_time is user-friendly if available
        start_time_disp = previously_selected_appt.get(
            "start_time", "its scheduled time"
        )
        if "T" in start_time_disp and "Z" in start_time_disp:  # Basic check for ISO UTC
            try:
                from app.utils.time_utils import utc_iso_to_user_time  # Ensure import

                start_time_disp = utc_iso_to_user_time(
                    start_time_disp, user_state
                ).strftime("%I:%M %p on %b %d")
            except:
                pass

        return (
            f"I still have '{title}' at {start_time_disp} selected. "
            "What would you like to change about it? Or, are you looking for a different appointment?"
        )

    # Case 2: A new identifier is given, or no appointment was previously selected.
    # If a new identifier is given, clear any previously selected appointment.
    if appointment_identifier and previously_selected_appt:
        logger.info(
            f"New identifier '{appointment_identifier}' provided, clearing previously selected appointment."
        )
        previously_selected_appt = None
        ctx.session.userdata.selected_appointment_to_update = None

    # Case 2a: No specific identifier is provided by the user in this call
    if not appointment_identifier:
        if search_results:
            return (
                "Please choose an appointment from the list you'd like to update "
                "(e.g., 'number 1', or by its title/time)."
            )
        else:
            # LLM should call find_appointment first
            return (
                "Which appointment would you like to update? "
                "Please provide its title or approximate date/time so I can find it."
            )

    # Case 2b: An `appointment_identifier` is provided.
    # Try to resolve it from existing search_results first.
    if search_results:
        logger.info(
            f"Attempting to resolve '{appointment_identifier}' from existing search results ({len(search_results)} items)."
        )
        resolved_appt = _resolve_identifier_from_search_results(
            search_results, appointment_identifier, user_timezone, last_date_query
        )
        if resolved_appt:
            ctx.session.userdata.selected_appointment_to_update = resolved_appt
            title = resolved_appt.get("title", "that appointment")
            # Ensure start_time is user-friendly
            start_time_disp = resolved_appt.get("start_time", "its scheduled time")
            if "T" in start_time_disp and "Z" in start_time_disp:
                try:
                    from app.utils.time_utils import utc_iso_to_user_time

                    start_time_disp = utc_iso_to_user_time(
                        start_time_disp, user_state
                    ).strftime("%I:%M %p on %b %d")
                except:
                    pass
            return (
                f"Okay, I've selected '{title}' at {start_time_disp}. "
                "What specific details would you like to change (e.g., time, title, notes)?"
            )
        else:
            # Identifier didn't match anything in the current list.
            # This could mean the user is referring to something new, or made a typo.
            # Let LLM decide if it should re-prompt for selection from list, or treat identifier as new search.
            return (
                f"I couldn't find an appointment matching '{appointment_identifier}' in the current list. "
                "Please try selecting by number, or provide a new title/date to search for a different appointment."
            )

    # Case 2c: No `search_results` in session, but `appointment_identifier` is provided.
    # This implies the LLM might expect this tool to also find the appointment.
    # This is where your "direct title lookup optimization" can fit.
    if not search_results and appointment_identifier:
        logger.info(
            f"No prior search results. Treating '{appointment_identifier}' as a potential direct lookup query."
        )
        is_potential_direct_title = (
            len(appointment_identifier.split()) >= 1  # Allow single word titles too
            and not appointment_identifier.isdigit()
            and not appointment_identifier.startswith("...")
        )
        if is_potential_direct_title:
            logger.info(
                f"Attempting direct API lookup for title: '{appointment_identifier}'"
            )
            try:
                found_appts = await appointment_api_service.get_appointments_by_user(
                    user_id=user_state.user_id,
                    title=appointment_identifier,  # Search by title
                    limit=5,  # Keep limit small for direct lookups
                    active_only=True,  # Typically for updates, focus on active/future
                )
                if len(found_appts) == 1:
                    # Store original ISO times for later duration calculation if needed
                    if "start_time" in found_appts[0]:
                        found_appts[0]["start_time_iso_utc"] = found_appts[0][
                            "start_time"
                        ]
                    if "end_time" in found_appts[0]:
                        found_appts[0]["end_time_iso_utc"] = found_appts[0]["end_time"]

                    # Convert times to user-friendly for session and prompt
                    from app.utils.time_utils import (
                        utc_iso_to_user_time,
                    )  # Ensure import

                    if found_appts[0].get("start_time_iso_utc"):
                        found_appts[0]["start_time"] = utc_iso_to_user_time(
                            found_appts[0]["start_time_iso_utc"], user_state
                        ).strftime("%I:%M %p on %b %d, %Y")
                    if found_appts[0].get("end_time_iso_utc"):
                        found_appts[0]["end_time"] = utc_iso_to_user_time(
                            found_appts[0]["end_time_iso_utc"], user_state
                        ).strftime("%I:%M %p on %b %d, %Y")

                    ctx.session.userdata.selected_appointment_to_update = found_appts[0]
                    # Also populate search_results so it's available if user wants to pick another later
                    ctx.session.userdata.current_appointment_search_results = (
                        found_appts
                    )
                    title = found_appts[0].get("title", "that appointment")
                    start_time_disp = found_appts[0].get(
                        "start_time", "its scheduled time"
                    )
                    return (
                        f"Okay, I found and selected '{title}' at {start_time_disp}. "
                        "What would you like to change about it?"
                    )
                elif len(found_appts) > 1:
                    # Store original ISO times
                    from app.utils.time_utils import utc_iso_to_user_time

                    for appt_item in found_appts:
                        if "start_time" in appt_item:
                            appt_item["start_time_iso_utc"] = appt_item["start_time"]
                        if "end_time" in appt_item:
                            appt_item["end_time_iso_utc"] = appt_item["end_time"]
                        if appt_item.get("start_time_iso_utc"):
                            appt_item["start_time"] = utc_iso_to_user_time(
                                appt_item["start_time_iso_utc"], user_state
                            ).strftime("%I:%M %p on %b %d, %Y")

                    ctx.session.userdata.current_appointment_search_results = (
                        found_appts
                    )
                    response_lines = [
                        f"I found a few appointments matching '{appointment_identifier}'. Which one do you mean?"
                    ]
                    for i, appt_item in enumerate(found_appts):
                        title_disp = appt_item.get("title", "Untitled")
                        time_disp = appt_item.get("start_time", "No time set")
                        response_lines.append(f"{i+1}. '{title_disp}' at {time_disp}")
                    return "\n".join(response_lines)
                else:
                    # No direct match by title. LLM should call find_appointment with more criteria.
                    return (
                        f"I couldn't find an appointment specifically titled '{appointment_identifier}'. "
                        "Try providing the date as well, or I can do a broader search if you like."
                    )
            except Exception as e:
                logger.error(
                    f"Error during direct API lookup for '{appointment_identifier}': {e}",
                    exc_info=True,
                )
                return "I had a problem trying to look that up. Could you try searching with more details?"
        else:  # Identifier is not title-like (e.g. just "tomorrow") and no search results
            return (
                f"To update an appointment based on '{appointment_identifier}', I need to search for it first. "
                "Could you provide the title of the appointment as well?"
            )  # This guides LLM to call find_appointment

    # Fallback if no other condition met (should be rare if logic above is complete)
    return "I'm a bit confused about which appointment to update. Could you please clarify?"


@function_tool
async def apply_appointment_updates(
    ctx: UserDataContext,
    new_notes_or_summary: Optional[str] = None,
    new_title: Optional[str] = None,
    new_start_time_desc: Optional[str] = None,
    new_location: Optional[str] = None,
    new_participants: Optional[
        List[str]
    ] = None,  # Assumed to be IDs for simplicity here
    new_is_recurring: Optional[bool] = None,
    new_recurrence_rule: Optional[str] = None,
    new_notify_before: Optional[int] = None,
) -> str:
    """
    Applies the specified updates to the appointment currently selected in the session
    (i.e., `ctx.session.userdata.selected_appointment_to_update`).

    Args:
        ctx: User data context.
        new_... (various fields): The new values for the appointment.

    Returns:
        A string message indicating success, failure, or need for more info.
    """
    # Handle session data access based on structure
    if not ctx.session or not ctx.session.userdata:
        raise ToolError("Internal session error: User context not available.")

    # Get user state with robust access pattern
    user_state = None
    if hasattr(ctx.session.userdata, "user_state"):
        user_state = ctx.session.userdata.user_state
    elif (
        isinstance(ctx.session.userdata, dict)
        and "session_data" in ctx.session.userdata
    ):
        user_state = ctx.session.userdata["session_data"].user_state
    else:
        raise ToolError("Unable to access user state from session data.")

    if not user_state or not user_state.user_id:
        raise ToolError("User ID not found. Cannot update appointments.")

    logger.info(
        f"apply_appointment_updates called with new_title: '{new_title}', "
        f"new_start_time: '{new_start_time_desc}', new_notes: '{new_notes_or_summary}'"
    )

    selected_appt_data = getattr(
        ctx.session.userdata, "selected_appointment_to_update", None
    )

    if not selected_appt_data:
        raise ToolError(
            "No appointment is currently selected for update. "
            "Please select an appointment first using its title, date, or by picking from a list."
        )

    has_new_update_fields = any(
        [
            new_notes_or_summary is not None,
            new_title is not None,
            new_start_time_desc is not None,
            new_location is not None,
            new_participants is not None,
            new_is_recurring is not None,
            new_recurrence_rule is not None,
            new_notify_before is not None,
        ]
    )

    original_title_for_prompt = selected_appt_data.get(
        "title", "the selected appointment"
    )
    if not has_new_update_fields:
        logger.info(
            f"Appointment '{original_title_for_prompt}' is selected, but no update fields provided to apply_appointment_updates."
        )
        return (
            f"Okay, I have '{original_title_for_prompt}' selected. "
            "What specific details would you like to change (e.g., the time, title, add notes)?"
        )

    # --- Proceed with update ---
    appt_to_update_id = str(selected_appt_data.get("_id"))
    if not appt_to_update_id or appt_to_update_id == "None":  # Check if _id was missing
        logger.error(
            f"Selected appointment data is missing an '_id': {selected_appt_data}"
        )
        raise ToolError(
            "The selected appointment is missing a valid ID. Cannot update."
        )

    logger.info(
        f"Proceeding to update appointment ID: {appt_to_update_id} (Title: {original_title_for_prompt})"
    )
    user_timezone = get_user_timezone(user_state)
    now_utc = datetime.now(timezone.utc)
    now_local = datetime.now(user_timezone)

    update_payload = {}
    is_primarily_notes_update = (
        new_notes_or_summary is not None
        and new_title is None
        and new_start_time_desc is None
    )

    if (
        new_participants is not None
    ):  # Simplified: assumes IDs. Real flow would resolve names.
        update_payload["participant_ids"] = new_participants
    if new_title is not None:
        update_payload["title"] = new_title
    if new_location is not None:
        update_payload["location"] = new_location
    if new_notes_or_summary is not None:
        update_payload["summary"] = new_notes_or_summary

    new_start_time_utc_obj = None
    if new_start_time_desc is not None:
        try:
            from app.utils.time_utils import user_time_to_utc_iso  # Ensure import

            original_is_recurring = selected_appt_data.get("is_recurring", False)
            allow_past_for_parsing = original_is_recurring or is_primarily_notes_update

            # Log the time description for debugging
            logger.info(f"Updating appointment time to: '{new_start_time_desc}'")

            new_time_utc_iso_str = user_time_to_utc_iso(
                new_start_time_desc, user_state, allow_past=allow_past_for_parsing
            )
            new_start_time_utc_obj = datetime.fromisoformat(
                new_time_utc_iso_str.replace("Z", "+00:00")
            )

            # Store the parsed time for reference
            start_time_local = new_start_time_utc_obj.astimezone(user_timezone)
            parsed_time_data = {
                "utc": new_start_time_utc_obj,
                "local": start_time_local,
                "formatted": start_time_local.strftime("%I:%M %p on %b %d"),
                "original_desc": new_start_time_desc,
            }
            # Handle session data storage based on structure
            if hasattr(ctx.session.userdata, "parsed_update_time"):
                ctx.session.userdata.parsed_update_time = parsed_time_data
            elif isinstance(ctx.session.userdata, dict):
                ctx.session.userdata["parsed_update_time"] = parsed_time_data

            # Log the parsed time for debugging
            logger.info(
                f"Parsed time '{new_start_time_desc}' to {start_time_local.strftime('%I:%M %p on %b %d')}"
            )

            current_recurrence_status = (
                new_is_recurring
                if new_is_recurring is not None
                else original_is_recurring
            )
            if (
                new_start_time_utc_obj <= now_utc
                and not current_recurrence_status
                and not is_primarily_notes_update
            ):
                # ... (same future time validation as before)
                if new_start_time_utc_obj.date() == now_utc.date():
                    sugg_time = (
                        (new_start_time_utc_obj + timedelta(days=1))
                        .astimezone(user_timezone)
                        .strftime("%I:%M %p on %b %d")
                    )
                    raise ToolError(
                        f"The time '{new_start_time_desc}' has already passed today. Did you mean {sugg_time}?"
                    )
                raise ToolError(
                    f"The time '{new_start_time_desc}' is in the past. Please provide a future time like 'tomorrow at 3pm' or 'next Monday at 10am'."
                )

            update_payload["start_time"] = new_start_time_utc_obj.isoformat().replace(
                "+00:00", "Z"
            )

            # Preserve duration
            orig_start_iso = selected_appt_data.get(
                "start_time_iso_utc"
            ) or selected_appt_data.get(
                "start_time"
            )  # Prefer raw ISO if stored
            orig_end_iso = selected_appt_data.get(
                "end_time_iso_utc"
            ) or selected_appt_data.get("end_time")
            try:
                # Ensure these are valid ISO UTC strings before parsing
                if not (
                    orig_start_iso
                    and "T" in orig_start_iso
                    and "Z" in orig_start_iso
                    and orig_end_iso
                    and "T" in orig_end_iso
                    and "Z" in orig_end_iso
                ):
                    # If original times from selected_appt_data are user-friendly, this will fail.
                    # This highlights importance of storing _iso_utc versions in selected_appt_data
                    logger.warning(
                        "Original start/end times in selected_appt_data are not in expected ISO UTC format for duration calculation."
                    )
                    raise ValueError("Original times not in ISO UTC format")

                original_start_dt = datetime.fromisoformat(
                    orig_start_iso.replace("Z", "+00:00")
                )
                original_end_dt = datetime.fromisoformat(
                    orig_end_iso.replace("Z", "+00:00")
                )
                duration = original_end_dt - original_start_dt
                update_payload["end_time"] = (
                    (new_start_time_utc_obj + duration)
                    .isoformat()
                    .replace("+00:00", "Z")
                )
            except Exception as e_dur:
                logger.warning(
                    f"Could not preserve duration using {orig_start_iso}/{orig_end_iso}: {e_dur}. Defaulting to 1 hour."
                )
                update_payload["end_time"] = (
                    (new_start_time_utc_obj + timedelta(hours=1))
                    .isoformat()
                    .replace("+00:00", "Z")
                )

            # Recurrence
            if new_is_recurring is not None:
                update_payload["is_recurring"] = new_is_recurring
                update_payload["recurrence_rule"] = (
                    new_recurrence_rule
                    if new_is_recurring and new_recurrence_rule
                    else None
                )
            elif new_recurrence_rule is not None:
                update_payload["is_recurring"] = True
                update_payload["recurrence_rule"] = new_recurrence_rule

        except ToolError as te_time:
            raise te_time
        except ValueError as e_time_parse:  # From dateutil_parse or fromisoformat
            logger.error(
                f"Time parsing error for '{new_start_time_desc}': {e_time_parse}"
            )
            # Try to use current time as default if date parsing failed
            try:
                # Extract date part from new_start_time_desc if possible
                # This assumes new_start_time_desc might contain a date without a time
                current_time = now_local.strftime("%H:%M")
                combined_time = f"{new_start_time_desc} {current_time}"

                logger.info(f"Trying with current time as default: {combined_time}")
                new_time_utc_iso_str = user_time_to_utc_iso(
                    combined_time, user_state, allow_past=allow_past_for_parsing
                )
                new_start_time_utc_obj = datetime.fromisoformat(
                    new_time_utc_iso_str.replace("Z", "+00:00")
                )

                # If we get here, the fallback worked
                logger.info(
                    f"Successfully used current time as default: {new_time_utc_iso_str}"
                )

                # Continue with the time validation
                if (
                    new_start_time_utc_obj <= now_utc
                    and not current_recurrence_status
                    and not is_primarily_notes_update
                ):
                    if new_start_time_utc_obj.date() == now_utc.date():
                        sugg_time = (
                            (new_start_time_utc_obj + timedelta(days=1))
                            .astimezone(user_timezone)
                            .strftime("%I:%M %p on %b %d")
                        )
                        raise ToolError(
                            f"The time '{combined_time}' has already passed today. Did you mean {sugg_time}?"
                        )
                    raise ToolError(
                        f"The time '{combined_time}' is in the past. Please provide a future time like 'tomorrow at 3pm' or 'next Monday at 10am'."
                    )

                # Update the payload with the new time
                update_payload["start_time"] = (
                    new_start_time_utc_obj.isoformat().replace("+00:00", "Z")
                )

                # Set end time (1 hour later by default)
                update_payload["end_time"] = (
                    (new_start_time_utc_obj + timedelta(hours=1))
                    .isoformat()
                    .replace("+00:00", "Z")
                )

            except Exception as fallback_error:
                # If fallback also fails, raise the original error
                logger.error(f"Fallback time parsing also failed: {fallback_error}")
                raise ToolError(
                    f"I couldn't understand the time '{new_start_time_desc}'. Please provide it clearly."
                )
        except Exception as e_time_other:
            logger.error(
                f"Unexpected error processing new time '{new_start_time_desc}': {e_time_other}",
                exc_info=True,
            )
            raise ToolError(
                "Sorry, an issue occurred with the new time. Please try again."
            )

    if new_start_time_utc_obj or new_notify_before is not None:
        minutes_before = (
            new_notify_before
            if new_notify_before is not None
            else selected_appt_data.get(
                "notify_before", DEFAULT_APPOINTMENT_NOTIFY_BEFORE
            )
        )
        update_payload["notify_before"] = minutes_before

    if not update_payload:
        return "No actual changes were specified. Nothing was updated."

    # --- Perform API call and finalize ---
    try:
        logger.info(
            f"Updating appointment {appt_to_update_id} with payload: {update_payload}"
        )
        updated_appt_response = await appointment_api_service.update_appointment(
            appt_to_update_id, update_payload
        )
        if not updated_appt_response:
            raise ToolError("Database update failed or did not confirm changes.")

        final_updated_title = updated_appt_response.get(
            "title", original_title_for_prompt
        )

        # Update linked reminders (logic remains same as your original)
        if new_start_time_utc_obj or new_notify_before is not None:
            # ... (reminder update logic) ...
            try:
                linked_reminders = (
                    await reminder_api_service.get_reminders_by_linked_entity(
                        user_state.user_id, appt_to_update_id
                    )
                )
                for rem_data in linked_reminders:
                    rem_id = str(rem_data.get("_id"))
                    reminder_update_payload = {}
                    current_notify_before = updated_appt_response.get(
                        "notify_before", DEFAULT_APPOINTMENT_NOTIFY_BEFORE
                    )
                    reminder_update_payload["notify_before"] = current_notify_before

                    if new_start_time_utc_obj:
                        reminder_trigger_time = new_start_time_utc_obj - timedelta(
                            minutes=current_notify_before
                        )
                        reminder_update_payload["trigger_time"] = (
                            reminder_trigger_time.isoformat().replace("+00:00", "Z")
                        )

                    if reminder_update_payload:
                        await reminder_api_service.update_reminder(
                            rem_id, reminder_update_payload
                        )
                        logger.info(
                            f"Updated linked reminder {rem_id} for appt {appt_to_update_id}"
                        )
            except Exception as e_rem:
                logger.error(
                    f"Failed to update linked reminders for appt {appt_to_update_id}: {e_rem}"
                )

        # Send frontend event (logic remains same)
        try:
            # ... (send_event logic) ...
            event_data = {
                "appointmentId": appt_to_update_id,
                "title": final_updated_title,
                "startTime": updated_appt_response.get("start_time"),
                "endTime": updated_appt_response.get("end_time"),
            }  # etc.
            await send_event(
                room=ctx.session.userdata.room,
                action_type=ActionType.UPDATE_APPOINTMENT,
                message=f"Appointment '{final_updated_title}' updated.",
                data=event_data,
                status=ActionStatus.SUCCESS,
            )
        except Exception as event_err:
            logger.error(f"Failed to send UPDATE_APPOINTMENT event: {event_err}")

        # Clear session state
        logger.info("Clearing appointment session state after successful update.")
        # Handle session data clearing based on structure
        if hasattr(ctx.session.userdata, "current_appointment_search_results"):
            ctx.session.userdata.current_appointment_search_results = []
        elif isinstance(ctx.session.userdata, dict):
            ctx.session.userdata["current_appointment_search_results"] = []

        if hasattr(ctx.session.userdata, "selected_appointment_to_update"):
            ctx.session.userdata.selected_appointment_to_update = None
        elif isinstance(ctx.session.userdata, dict):
            ctx.session.userdata["selected_appointment_to_update"] = None

        if hasattr(ctx.session.userdata, "last_appointment_date_query"):
            ctx.session.userdata.last_appointment_date_query = None
        elif isinstance(ctx.session.userdata, dict):
            ctx.session.userdata["last_appointment_date_query"] = None
        # if hasattr(ctx.session.userdata, "in_appointment_update_flow"):
        #    ctx.session.userdata.in_appointment_update_flow = False # Optional flag

        # Add information about the updated time if applicable
        time_info = ""
        if new_start_time_desc:
            parsed_time = None
            if hasattr(ctx.session.userdata, "parsed_update_time"):
                parsed_time = ctx.session.userdata.parsed_update_time
            elif isinstance(ctx.session.userdata, dict):
                parsed_time = ctx.session.userdata.get("parsed_update_time")

            if parsed_time:
                time_info = (
                    f" The appointment is now set for {parsed_time.get('formatted')}."
                )

        return f"Okay, the appointment '{final_updated_title}' has been updated successfully.{time_info}"

    except ToolError as te:
        logger.warning(f"ToolError during apply_appointment_updates: {te}")
        raise te
    except Exception as e:
        logger.error(
            f"Unexpected API update error for ID {appt_to_update_id}: {e}",
            exc_info=True,
        )
        raise ToolError(
            "Sorry, an unexpected problem occurred while updating the appointment."
        )


@function_tool
async def list_all_appointments(
    ctx: UserDataContext,
    search_intent: Optional[str] = None,  # Optional intent hint from LLM
) -> str:
    """
    Lists appointments for the user, typically current/future ones. Uses `find_appointment`.

    Args:
        ctx: The run context.
        search_intent: (Optional) Pass "update_notes" to include past appointments.

    Returns:
        A string listing relevant appointments or a message if none are found.
    """
    # (Keep this function as it was - it correctly delegates to find_appointment)
    # Get user state with robust access pattern
    user_state = get_user_state_from_context(ctx)
    user_id = user_state.user_id
    log_message = f"list_all_appointments called for user {user_id}."
    if search_intent:
        log_message += f" Intent hint: {search_intent}"
    logger.info(log_message)

    # Delegate to find_appointment with no specific queries
    return await find_appointment(
        ctx=ctx,
        title_query=None,
        date_query=None,
        time_query=None,
        search_intent=search_intent,  # Pass hint through
    )


@function_tool
async def find_contact_for_appointment(
    ctx: UserDataContext,
    contact_name: str,
) -> str:
    """
    Searches for a contact by name for appointment participants.
    Returns contact ID if found, prompts for clarification if multiple, or indicates not found.

    Args:
        ctx: User data context.
        contact_name: Name to search for.

    Returns:
        - "CONTACT_FOUND:<contact_id>" for single match
        - "CONTACT_NOT_FOUND" if none found
        - "MULTIPLE_CONTACTS_FOUND:<options>" for multiple matches (no IDs in prompt)
    """
    # Get user state with robust access pattern
    user_state = get_user_state_from_context(ctx)
    user_id = user_state.user_id
    if not user_id:
        raise ToolError("User ID not found.")
    if not contact_name or not contact_name.strip():
        raise ToolError("Please provide a contact name.")

    # Check if contact_name looks like an ID (starts with numbers/letters and is 24 chars long)
    # If so, this is likely a contact ID that was passed incorrectly, so we should return not found
    if re.match(r"^[0-9a-f]{24}$", contact_name.strip()):
        logger.warning(
            f"Contact name '{contact_name}' appears to be an ID, not a name. Returning not found."
        )
        return "CONTACT_NOT_FOUND"

    # Check if we already have a selected contact in the session
    selected_contact = getattr(
        ctx.session.userdata, "selected_appointment_contact", None
    )
    if (
        selected_contact
        and selected_contact.get("name", "").lower() == contact_name.strip().lower()
    ):
        # We already have this contact selected, return its ID directly
        contact_id = str(selected_contact.get("_id"))
        logger.info(
            f"Using already selected contact: {selected_contact.get('name')} (ID: {contact_id})"
        )
        return f"CONTACT_FOUND:{contact_id}"

    logger.info(f"Searching for contact: {contact_name} for user {user_id}")

    try:
        contacts = await contact_api_service.search_contacts_by_name(
            user_id, contact_name.strip()
        )
        # Handle session data storage based on structure
        if hasattr(ctx.session.userdata, "appointment_contact_search_results"):
            ctx.session.userdata.appointment_contact_search_results = contacts
        elif isinstance(ctx.session.userdata, dict):
            ctx.session.userdata["appointment_contact_search_results"] = contacts

        if not contacts:
            logger.info(f"No contacts found for '{contact_name}'.")
            return "CONTACT_NOT_FOUND"

        elif len(contacts) == 1:
            contact_id = str(contacts[0].get("_id"))
            # Handle session data storage based on structure
            if hasattr(ctx.session.userdata, "selected_appointment_contact"):
                ctx.session.userdata.selected_appointment_contact = contacts[0]
            elif isinstance(ctx.session.userdata, dict):
                ctx.session.userdata["selected_appointment_contact"] = contacts[0]
            logger.info(f"Found contact: {contacts[0].get('name')} (ID: {contact_id})")
            return f"CONTACT_FOUND:{contact_id}"

        else:
            # Format options without internal IDs
            options = []
            for i, contact in enumerate(contacts[:5]):
                contact_info = f"{i+1}. {contact.get('name', 'Unknown')}"
                phone_numbers = contact.get("phone_numbers", [])
                if (
                    phone_numbers
                    and isinstance(phone_numbers, list)
                    and phone_numbers[0].get("number")
                ):
                    contact_info += f" - {phone_numbers[0]['number']}"
                elif contact.get("emails", []) and contact["emails"][0].get("address"):
                    contact_info += f" - {contact['emails'][0]['address']}"
                options.append(contact_info)
            logger.info(f"Multiple contacts found: {options}")
            return f"MULTIPLE_CONTACTS_FOUND:{'; '.join(options)}"

    except Exception as e:
        logger.error(f"Error searching contact '{contact_name}': {e}", exc_info=True)
        raise ToolError(f"Error searching for contact: {str(e)}")


@function_tool
async def confirm_contact_for_appointment(
    ctx: UserDataContext,
    selection: str,
) -> str:
    """
    Confirms which contact to use when multiple matches were found.

    Args:
        ctx: The run context.
        selection: User's selection (e.g., "1", "first", "John Smith").

    Returns:
        A string indicating the result of the confirmation:
        - "CONTACT_CONFIRMED:<contact_id>" if a contact is confirmed
        - "CONTACT_CONFIRMATION_FAILED" if confirmation fails
    """
    if not ctx.session or not ctx.session.userdata:
        raise ToolError("Internal session error.")

    # Get the search results from the session
    search_results = getattr(
        ctx.session.userdata, "appointment_contact_search_results", []
    )

    if not search_results:
        logger.error("Confirmation called, but no search results in session.")
        return "CONTACT_CONFIRMATION_FAILED"

    logger.info(
        f"Confirming contact selection: {selection} from {len(search_results)} options."
    )

    # Try to match the selection to a contact
    matched_contact = None
    selection_lower = selection.lower().strip()

    # Try matching by index (number)
    parsed_idx = None
    if selection_lower.startswith("number "):
        num_part = selection_lower.split("number ")[1]
    elif selection_lower.endswith(("st", "nd", "rd", "th")):
        num_part = "".join(filter(str.isdigit, selection_lower))
    elif selection_lower.isdigit():
        num_part = selection_lower
    else:
        num_part = None

    if num_part and num_part.isdigit():
        parsed_idx = int(num_part) - 1
        if not (0 <= parsed_idx < len(search_results)):
            parsed_idx = None

    # Try matching by ordinal words
    if parsed_idx is None:
        ordinal_map = {
            "first": 0,
            "second": 1,
            "third": 2,
            "fourth": 3,
            "fifth": 4,
            "one": 0,
            "two": 1,
            "three": 2,
            "four": 3,
            "five": 4,
            "last": len(search_results) - 1,
        }
        cleaned_selection = re.sub(r"^(the|number)\s+", "", selection_lower)
        if cleaned_selection in ordinal_map:
            parsed_idx = ordinal_map[cleaned_selection]
            if not (0 <= parsed_idx < len(search_results)):
                parsed_idx = None

    if parsed_idx is not None:
        matched_contact = search_results[parsed_idx]
        logger.info(f"Matched by index/ordinal: {parsed_idx + 1}")

    # If no index match, try matching by name
    if not matched_contact:
        for contact in search_results:
            if selection_lower == contact.get("name", "").lower():
                matched_contact = contact
                logger.info("Matched by exact name in results.")
                break

    if matched_contact:
        contact_id = str(matched_contact.get("_id"))

        # Store the selected contact in the session
        # Handle session data storage based on structure
        if hasattr(ctx.session.userdata, "selected_appointment_contact"):
            ctx.session.userdata.selected_appointment_contact = matched_contact
        elif isinstance(ctx.session.userdata, dict):
            ctx.session.userdata["selected_appointment_contact"] = matched_contact

        # Also update pending appointment data if it exists
        pending_data = getattr(ctx.session.userdata, "pending_appointment_data", {})
        if pending_data:
            # Add this contact ID to participant_ids if it exists
            participant_ids = pending_data.get("participant_ids", [])
            if contact_id not in participant_ids:
                participant_ids.append(contact_id)
                pending_data["participant_ids"] = participant_ids
                # Handle session data storage based on structure
                if hasattr(ctx.session.userdata, "pending_appointment_data"):
                    ctx.session.userdata.pending_appointment_data = pending_data
                elif isinstance(ctx.session.userdata, dict):
                    ctx.session.userdata["pending_appointment_data"] = pending_data
                logger.info(
                    f"Updated pending appointment data with contact ID: {contact_id}"
                )

        logger.info(
            f"Confirmed contact: {matched_contact.get('name')} (ID: {contact_id})"
        )
        return f"CONTACT_CONFIRMED:{contact_id}"
    else:
        logger.warning(f"Could not identify contact from '{selection}'.")
        return "CONTACT_CONFIRMATION_FAILED"


@function_tool
async def add_contact_for_appointment(
    ctx: UserDataContext,
    first_name: str,
    last_name: Optional[str] = None,
    phone_number: Optional[str] = None,
    relationship: str = "doctor",
) -> str:
    """
    Creates a new contact to be added as a participant to an appointment.

    Args:
        ctx: The run context.
        first_name: First name of the contact.
        last_name: Last name of the contact.
        phone_number: Phone number of the contact.
        relationship: Relationship of the contact (default: "doctor").

    Returns:
        A string indicating the result of the creation:
        - "CONTACT_CREATED:<contact_id>" if contact is created successfully
        - "CONTACT_CREATION_FAILED" if creation fails
    """
    # Get user state with robust access pattern
    user_state = get_user_state_from_context(ctx)

    if relationship in ["father", "mother", "parents"]:
        relationship = "parent"

    user_id = user_state.user_id
    if not user_id:
        raise ToolError("User ID not found.")

    if not first_name or not first_name.strip():
        raise ToolError("Please provide a first name for the contact.")

    logger.info(
        f"Creating new contact: {first_name} {last_name or ''} for user {user_id}"
    )

    try:
        # Prepare contact data
        contact_data = {
            "user_id": user_id,
            "first_name": first_name.strip(),
            "last_name": last_name.strip() if last_name else "",
            "name": f"{first_name.strip()} {last_name.strip() if last_name else ''}".strip(),
            "relationship": relationship,
            "is_myhoudini": True,
        }

        # Add phone number if provided
        if phone_number:
            contact_data["phone_numbers"] = [
                {"number": phone_number.strip(), "type": "mobile"}
            ]

        # Create the contact
        created_contact = await contact_api_service.create_contact(contact_data)

        if not created_contact or not created_contact.get("_id"):
            logger.error("Database did not confirm the new contact creation.")
            return "CONTACT_CREATION_FAILED"

        contact_id = str(created_contact.get("_id"))

        # Store the created contact in the session
        # Handle session data storage based on structure
        if hasattr(ctx.session.userdata, "selected_appointment_contact"):
            ctx.session.userdata.selected_appointment_contact = created_contact
        elif isinstance(ctx.session.userdata, dict):
            ctx.session.userdata["selected_appointment_contact"] = created_contact

        # Also update pending appointment data if it exists
        pending_data = getattr(ctx.session.userdata, "pending_appointment_data", {})
        if pending_data:
            # Add this contact ID to participant_ids if it exists
            participant_ids = pending_data.get("participant_ids", [])
            if contact_id not in participant_ids:
                participant_ids.append(contact_id)
                pending_data["participant_ids"] = participant_ids
                # Handle session data storage based on structure
                if hasattr(ctx.session.userdata, "pending_appointment_data"):
                    ctx.session.userdata.pending_appointment_data = pending_data
                elif isinstance(ctx.session.userdata, dict):
                    ctx.session.userdata["pending_appointment_data"] = pending_data
                logger.info(
                    f"Updated pending appointment data with new contact ID: {contact_id}"
                )

        # Send event to frontend
        try:
            # Extract contact details
            event_details = extract_contact_details(created_contact)

            # Construct event data
            event_data = {
                "contactId": contact_id,
                "details": event_details,
            }

            # Handle room access with robust pattern
            room = None
            if hasattr(ctx.session.userdata, "room"):
                room = ctx.session.userdata.room
            elif (
                isinstance(ctx.session.userdata, dict)
                and "room" in ctx.session.userdata
            ):
                room = ctx.session.userdata["room"]

            if room:
                await send_event(
                    room=room,
                    action_type=ActionType.ADD_CONTACT,
                    message=f"Contact '{created_contact.get('name')}' added.",
                    data=event_data,
                    status=ActionStatus.SUCCESS,
                )
        except Exception as event_err:
            logger.error(f"Failed to send contact creation event: {event_err}")

        logger.info(
            f"Created new contact: {created_contact.get('name')} (ID: {contact_id})"
        )
        return f"CONTACT_CREATED:{contact_id}"

    except Exception as e:
        logger.error(f"Error creating contact: {e}", exc_info=True)
        return "CONTACT_CREATION_FAILED"


@function_tool
async def create_appointment(
    ctx: UserDataContext,
    title: str,
    start_time_desc: str,
    participants: List[str] = [],
    description: Optional[str] = None,
    notify_before: Optional[int] = None,
    participant_reminder: Optional[bool] = None,
    location: Optional[str] = None,
    is_recurring: Optional[bool] = False,
    recurrence_rule: Optional[str] = None,
) -> str:
    """
    Creates a new appointment with proper contact resolution for participants.

    IMPORTANT PARTICIPANT HANDLING GUIDELINES:
    1. When a user wants to create an appointment, ALWAYS ask if it's with a doctor, dentist,
       or other healthcare provider or person.
    2. If it's with a person, use the find_contact_for_appointment tool to search for the contact.
    3. If the contact is found, use their ID as a participant.
    4. If multiple contacts are found, use confirm_contact_for_appointment to let the user select one.
    5. If no contact is found, offer to create a new contact using add_contact_for_appointment.
    6. After handling the contact flow, continue with creating the appointment.

    Flow:
    1. Get title and ask context-specific questions
    2. Get date/time (must be future) , start_time_desc must be in future (like: "tomorrow 2pm", "next week")
    3. Handle participants:
       - If no participants, allow proceeding after confirmation
       - For each participant name:
         - Search contacts (Use find_contact_for_appointment tool)
         - Handle multiple matches (Use confirm_contact_for_appointment tool)
         - Create new contact if needed (Use add_contact_for_appointment tool)
    4. Get recurrence details if needed
    5. Get notification preferences
    6. Complete appointment creation

    IMPORTANT TIME HANDLING GUIDELINES:
    1. If the start_time_desc is unclear or ambiguous, ASK the user for clarification
       before calling this function. Request a specific format like "4pm today",
       "every day at 8am", or "Monday at 3pm".
    2. For past times on the current day, the system will reject non-recurring appointments.
       Instead, suggest setting it for tomorrow or a future date.
    3. For recurring appointments with times that would be in the past for the current day,
       the system will automatically set them for the next occurrence.
    4. Always validate that dates are in the future before calling this function.
    5. Use ISO 8601 format for explicit dates (e.g., "2025-04-23T08:30:00").
    6. The system will automatically handle timezone conversion based on the user's
       configured timezone.

    IMPORTANT NOTIFICATION GUIDELINES:
    1. Unlike reminders, appointments REQUIRE a notify_before parameter that specifies
       how many minutes before the appointment to send a notification.
    2. ALWAYS ask the user how many minutes before the appointment they want to be notified.
    3. If not specified by the user, default to 60 minutes.
    4. Common values are 10, 15, 30, or 60 minutes before the appointment.
    5. The system will automatically create a linked reminder with this notification time.

    Note: start_time_desc must be in future. handle LLM this on your end too if user say like next week, or on 20th may etc
    just take future time from that.
    """

    # Validate required fields
    if not title:
        return "Please provide a title for your appointment."

    if not start_time_desc:
        return "When should this appointment happen? Please provide date and time."

    # Check if we have a recently created appointment with the same title and time
    # to prevent duplicate creation
    if hasattr(ctx.session.userdata, "last_created_appointment"):
        last_appt = ctx.session.userdata.last_created_appointment
        if (
            last_appt.get("title") == title
            and last_appt.get("start_time_desc") == start_time_desc
        ):
            # This appears to be a duplicate creation attempt
            logger.warning(
                f"Detected potential duplicate appointment creation: {title} at {start_time_desc}"
            )

            # Format the time for display
            try:
                # Get user state with robust access pattern
                user_state = get_user_state_from_context(ctx)
                user_timezone = get_user_timezone(user_state)
                start_time_utc = datetime.fromisoformat(
                    last_appt.get("start_time").replace("Z", "+00:00")
                )
                start_time_local = start_time_utc.astimezone(user_timezone)
                time_str = start_time_local.strftime("%b %d at %I:%M %p")

                return (
                    f"I've already created an appointment for '{title}' on {time_str}. "
                    f"Is there anything else you'd like me to do?"
                )
            except Exception as e:
                logger.error(f"Error formatting time for duplicate check: {e}")
                # Continue with creation if we can't confirm it's a duplicate

    # Initialize session data
    if not hasattr(ctx.session.userdata, "pending_appointment_data"):
        ctx.session.userdata.pending_appointment_data = {}
    if not hasattr(ctx.session.userdata, "selected_appointment_contact"):
        ctx.session.userdata.selected_appointment_contact = None
    if not hasattr(ctx.session.userdata, "appointment_contact_search_results"):
        ctx.session.userdata.appointment_contact_search_results = []

    # Step 1: Handle participants
    participant_ids = []

    # Check if we have pending participant IDs from a previous call
    pending_data = getattr(ctx.session.userdata, "pending_appointment_data", {})
    if pending_data and "participant_ids" in pending_data:
        participant_ids = pending_data.get("participant_ids", [])
        logger.info(f"Using {len(participant_ids)} participant IDs from pending data")

    if participants and not (
        len(participants) == 1
        and participants[0].lower() in ["just me", "no", "none", "myself"]
    ):
        for name in participants:
            if not name.strip():
                continue

            # Check if this is a contact ID that we already have
            if re.match(r"^[0-9a-f]{24}$", name.strip()):
                logger.info(f"Using contact ID directly: {name}")
                participant_ids.append(name.strip())
                continue

            # Search for contact
            contact_result = await find_contact_for_appointment(ctx, name)

            # Handle different contact resolution cases
            if contact_result == "CONTACT_NOT_FOUND":
                ctx.session.userdata.pending_appointment_data = {
                    "title": title,
                    "start_time_desc": start_time_desc,
                    "pending_participant_name": name,
                    "participant_ids": participant_ids,  # Store any IDs we've already collected
                }
                return (
                    f"I couldn't find a contact named '{name}'. "
                    "Would you like to:\n"
                    "1. Try a different name\n"
                    "2. Create a new contact\n"
                    "3. Proceed without this participant"
                )

            elif contact_result.startswith("MULTIPLE_CONTACTS_FOUND:"):
                ctx.session.userdata.pending_appointment_data = {
                    "title": title,
                    "start_time_desc": start_time_desc,
                    "pending_participant_name": name,
                    "participant_ids": participant_ids,  # Store any IDs we've already collected
                }
                options = contact_result.split(":", 1)[1]
                return (
                    f"Multiple contacts found for '{name}':\n{options}\n"
                    "Please specify which one (e.g. 'number 1')"
                )

            elif contact_result.startswith("CONTACT_FOUND:"):
                contact_id = contact_result.split(":", 1)[1]
                if contact_id not in participant_ids:  # Avoid duplicates
                    participant_ids.append(contact_id)

    # Step 2: Validate time
    # Get user state with robust access pattern
    user_state = get_user_state_from_context(ctx)
    user_timezone = get_user_timezone(user_state)
    try:
        start_time_utc, _, _ = convert_local_to_utc(start_time_desc, user_timezone)

        # Store the parsed time in the session for reference
        start_time_local = start_time_utc.astimezone(user_timezone)
        # Handle session data storage based on structure
        if hasattr(ctx.session.userdata, "parsed_appointment_time"):
            ctx.session.userdata.parsed_appointment_time = {
                "utc": start_time_utc,
                "local": start_time_local,
                "formatted": start_time_local.strftime("%I:%M %p on %b %d"),
                "original_desc": start_time_desc,
            }
        elif isinstance(ctx.session.userdata, dict):
            ctx.session.userdata["parsed_appointment_time"] = {
                "utc": start_time_utc,
                "local": start_time_local,
                "formatted": start_time_local.strftime("%I:%M %p on %b %d"),
                "original_desc": start_time_desc,
            }

        if start_time_utc < datetime.now(timezone.utc):
            return "Please choose a time in the future. When should this happen?"

        # Log the parsed time for debugging
        logger.info(
            f"Parsed time '{start_time_desc}' to {start_time_local.strftime('%I:%M %p on %b %d')}"
        )
    except Exception as e:
        logger.error(f"Time parsing error: {e}")
        return f"I didn't understand that time. Please try again with a clear format like 'tomorrow 2pm', 'next Monday at 10am', or '3pm on Friday'."

    # Step 3: Handle participant reminders
    if participant_ids and participant_reminder is None:
        # Store all current data in pending_appointment_data
        ctx.session.userdata.pending_appointment_data = {
            "title": title,
            "start_time_desc": start_time_desc,
            "participant_ids": participant_ids,
            "is_recurring": is_recurring,
            "recurrence_rule": recurrence_rule,
            "description": description,
            "location": location,
            "notify_before": notify_before,
        }
        logger.info(
            f"Asking about participant reminders for {len(participant_ids)} participants"
        )
        return "Should I remind you about the participants before the appointment? (yes/no)"

    # Step 4: Handle recurrence
    if is_recurring and not recurrence_rule:
        recurrence_rule = "FREQ=WEEKLY;INTERVAL=1"

    # Step 5: Create appointment
    try:
        end_time_utc = start_time_utc + timedelta(hours=1)
        user_id = user_state.user_id

        appointment_data = {
            "user_id": user_id,
            "title": title,
            "start_time": start_time_utc.isoformat(),
            "end_time": end_time_utc.isoformat(),
            "participant_ids": participant_ids,
            "location": location,
            "description": description,
            "is_recurring": is_recurring,
            "recurrence_rule": recurrence_rule if is_recurring else None,
            "notify_before": (
                notify_before
                if notify_before is not None
                else DEFAULT_APPOINTMENT_NOTIFY_BEFORE
            ),
        }

        # Call API to create appointment
        created_appointment = await appointment_api_service.create_appointment_function(
            appointment_data
        )

        if not created_appointment or not created_appointment.get("_id"):
            raise ToolError("Failed to create appointment in database")

        # Send event
        try:
            event_data = {
                "appointmentId": str(created_appointment.get("_id")),
                "title": title,
                "startTime": start_time_utc.isoformat(),
                "endTime": end_time_utc.isoformat(),
                "isRecurring": is_recurring,
                "recurrenceRule": recurrence_rule if is_recurring else None,
            }
            # Handle room access with robust pattern
            room = None
            if hasattr(ctx.session.userdata, "room"):
                room = ctx.session.userdata.room
            elif (
                isinstance(ctx.session.userdata, dict)
                and "room" in ctx.session.userdata
            ):
                room = ctx.session.userdata["room"]

            if room:
                await send_event(
                    room=room,
                    action_type=ActionType.CREATE_APPOINTMENT,
                    message=f"Appointment '{title}' created.",
                    data=event_data,
                    status=ActionStatus.SUCCESS,
                )
        except Exception as event_err:
            logger.error(f"Failed to send CREATE_APPOINTMENT event: {event_err}")

        # Create linked reminder
        try:
            reminder_payload = {
                "user_id": user_id,
                "reminder_text": title,
                "trigger_time": (
                    start_time_utc
                    - timedelta(
                        minutes=(
                            notify_before
                            if notify_before is not None
                            else DEFAULT_APPOINTMENT_NOTIFY_BEFORE
                        )
                    )
                ).isoformat(),
                "reminder_type": "appointment",
                "linked_entity": str(created_appointment.get("_id")),
                "participant_reminder": participant_reminder or False,
                "is_recurring": is_recurring,
                "recurrence_rule": recurrence_rule if is_recurring else None,
                "description": description,
            }
            await reminder_api_service.create_reminder(reminder_payload)
        except Exception as e:
            logger.error(f"Failed to create reminder: {e}")

        # Store the created appointment in the session to prevent duplicates
        ctx.session.userdata.last_created_appointment = {
            "id": str(created_appointment.get("_id")),
            "title": title,
            "start_time": start_time_utc.isoformat(),
            "start_time_desc": start_time_desc,
            "participant_ids": participant_ids,
        }

        # Clear other session state to prevent duplicate creation
        if hasattr(ctx.session.userdata, "pending_appointment_data"):
            ctx.session.userdata.pending_appointment_data = {}
        if hasattr(ctx.session.userdata, "selected_appointment_contact"):
            ctx.session.userdata.selected_appointment_contact = None
        if hasattr(ctx.session.userdata, "appointment_contact_search_results"):
            ctx.session.userdata.appointment_contact_search_results = []

        # Format confirmation
        start_time_local = start_time_utc.astimezone(user_timezone)
        time_str = start_time_local.strftime("%b %d at %I:%M %p")

        # Add a note about the original time description for clarity
        parsed_time = getattr(ctx.session.userdata, "parsed_appointment_time", None)
        original_desc = ""
        if parsed_time and parsed_time.get("original_desc") != start_time_desc:
            original_desc = (
                f" (from your request: '{parsed_time.get('original_desc')}')"
            )

        confirmation = f"✅ Appointment set: {title} on {time_str}{original_desc}"

        if participant_ids:
            confirmation += f" with {len(participant_ids)} participant(s)"
        if location:
            confirmation += f" at {location}"

        return confirmation

    except Exception as e:
        logger.error(f"Error creating appointment: {e}")
        return f"Sorry, I couldn't create the appointment: {str(e)}"


# --- Delete Appointment Tool ---


@function_tool
async def delete_appointment(
    ctx: UserDataContext,
    title_query: Optional[str] = None,
    date_query: Optional[str] = None,
    confirm_delete: Optional[bool] = None,
    selection: Optional[str] = None,
) -> str:
    """
    Conversational tool to delete an appointment by title and/or date, with user confirmation and timezone handling.

    IMPORTANT DELETION FLOW GUIDELINES:
    1. When a user wants to delete an appointment, follow these steps:
       a. First, ask for the appointment title if not provided
       b. If title is not known, ask for the date
       c. If date is provided, convert it to the user's timezone and search for appointments
       d. If multiple appointments are found, ask the user to confirm which one to delete
       e. Before deleting, ALWAYS ask for confirmation (e.g., "Are you sure you want to delete this appointment?")
       f. Only delete the appointment if the user confirms

    2. Time Handling:
       a. For date-only searches (like "May 20th"), include the entire day from 00:00 to 23:59
       b. Convert user's local time to UTC when passing to the API
       c. Convert UTC times from the API back to the user's local time for display

    Args:
        ctx: User data context.
        title_query: (Optional) Title of the appointment.
        date_query: (Optional) Date string (e.g., '20th May').
        confirm_delete: (Optional) Whether the user confirmed deletion.
        selection: (Optional) User selection if multiple appointments found.

    Returns:
        Prompt, confirmation, or result message.
    """
    from app.services import appointment_api_service as _appointment_api_service

    # 1. Ensure session and user state
    if (
        not ctx.session
        or not ctx.session.userdata
        or not ctx.session.userdata.user_state
    ):
        raise ToolError("Internal session error.")
    user_state = ctx.session.userdata.user_state
    user_id = user_state.user_id
    if not user_id:
        raise ToolError("User ID needed for deleting appointments.")

    # 2. Step 1: Ask for title if not provided, else ask for date
    if not title_query and not date_query:
        return "What is the title of the appointment you want to delete?"
    if not title_query and date_query:
        # If only date provided, ask for title for more precision
        return "Please provide the title of the appointment, or say 'no title' to search by date only."

    # 3. Step 2: Search for appointments
    user_timezone = get_user_timezone(user_state)
    now_local = datetime.now(user_timezone)
    parsed_date = None
    if date_query and date_query.lower() != "no title":
        parsed_date = parse_relative_date(date_query, now_local)
        if not parsed_date:
            return f"Sorry, I couldn't understand the date '{date_query}'. Please provide a clearer date, e.g., '20th May' or 'yesterday'."

    # Prepare search params
    search_params = {
        "user_id": user_id,
        "limit": 20,
        "active_only": True,  # Allow deleting past/future appointments
    }
    if title_query and title_query.lower() != "no title":
        search_params["title"] = title_query
    if parsed_date:
        # For date, search from 00:00 to 23:59 in user's timezone, convert to UTC
        start_dt_local = datetime.combine(parsed_date, datetime.min.time()).replace(
            tzinfo=user_timezone
        )
        end_dt_local = datetime.combine(parsed_date, datetime.max.time()).replace(
            tzinfo=user_timezone
        )
        start_dt_utc = start_dt_local.astimezone(timezone.utc)
        end_dt_utc = end_dt_local.astimezone(timezone.utc)
        search_params["start_date"] = start_dt_utc.strftime("%Y-%m-%d")
        search_params["end_date"] = end_dt_utc.strftime("%Y-%m-%d")

    # If we haven't already searched, do so and store results
    if (
        not hasattr(ctx.session.userdata, "current_appointment_delete_results")
        or not ctx.session.userdata.current_appointment_delete_results
    ):
        appointments = await _appointment_api_service.get_appointments_by_user(
            **search_params
        )
        if not appointments:
            return "No appointments found matching your criteria."
        ctx.session.userdata.current_appointment_delete_results = appointments

        # If multiple, prompt for selection
        if len(appointments) > 1:
            response_lines = [
                "Multiple appointments found. Which one do you want to delete?"
            ]
            for i, appt in enumerate(appointments[:5]):
                title = appt.get("title", "Untitled")
                time_str = appt.get("start_time", "")
                try:
                    start_dt_utc = datetime.fromisoformat(
                        time_str.replace("Z", "+00:00")
                    )
                    start_dt_local = start_dt_utc.astimezone(user_timezone)
                    time_disp = start_dt_local.strftime("%I:%M %p on %b %d, %Y")
                except Exception:
                    time_disp = time_str
                response_lines.append(f"{i+1}. '{title}' at {time_disp}")
            return "\n".join(response_lines)
        # If only one, store and proceed to confirmation
        ctx.session.userdata.selected_appointment_to_delete = appointments[0]
    else:
        appointments = ctx.session.userdata.current_appointment_delete_results

    # 4. If multiple, handle user selection
    if len(appointments) > 1 and selection:
        try:
            idx = int(selection) - 1
            if 0 <= idx < len(appointments):
                ctx.session.userdata.selected_appointment_to_delete = appointments[idx]
            else:
                return "Invalid selection. Please provide a valid number."
        except Exception:
            return "Invalid selection. Please provide a valid number."

    # 5. Confirm before delete
    appt = ctx.session.userdata.selected_appointment_to_delete
    appt_title = appt.get("title", "Untitled")
    if confirm_delete is None:
        return f"Are you sure you want to delete the appointment titled '{appt_title}'? (yes/no)"

    # 6. If confirmed, delete
    if confirm_delete:
        appt_id = str(appt.get("_id") or appt.get("id"))
        deleted = await _appointment_api_service.delete_appointment(appt_id)

        # Delete linked reminder
        try:
            reminders = await reminder_api_service.get_reminders_by_linked_entity(
                user_id, appt_id
            )
            for reminder in reminders:
                reminder_id = str(reminder.get("_id") or reminder.get("id"))
                await reminder_api_service.delete_reminder(reminder_id)
        except Exception as e:
            logger.error(f"Failed to delete linked reminder: {e}")

        # Clear session variables
        ctx.session.userdata.current_appointment_delete_results = []
        ctx.session.userdata.selected_appointment_to_delete = None
        if deleted:
            return f"Appointment '{appt_title}' has been deleted."
        else:
            return f"Failed to delete appointment '{appt_title}'."
    else:
        # User said no
        ctx.session.userdata.current_appointment_delete_results = []
        ctx.session.userdata.selected_appointment_to_delete = None
        return "Deletion cancelled."
