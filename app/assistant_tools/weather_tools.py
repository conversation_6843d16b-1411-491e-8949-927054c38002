# app/assistant_tools/weather_tools.py
from datetime import datetime, timedelta, timezone
from typing import Optional

from livekit.agents import Tool<PERSON>rror, function_tool

from app.assistant_tools.reminders_tools import find_reminder, list_all_reminders
from app.constants.action_types import ActionStatus, ActionType
from app.core.logger_setup import logger
from app.services import weather_api_service
from app.services.reminder_api_service import get_reminders
from app.state.session_data import UserDataContext
from app.utils.event_send import send_event
from app.utils.time_utils import (
    get_user_timezone,
)  # Use existing time utils for local context


@function_tool
async def get_weather(ctx: UserDataContext) -> str:
    """
    Fetches and provides a summary of today's weather for the user's location.
    Relies on a user's saved location in their profile.

    Args:
        ctx: The run context.

    Returns:
        A user-friendly string describing the weather, or an error message.
    """
    if not ctx.session or not ctx.session.userdata:
        raise ToolError("Internal session error.")

    user_state = ctx.session.userdata.user_state
    user_id = user_state.user_id

    if not user_id:
        raise ToolError("User ID not available for weather lookup.")

    logger.info(f"Fetching weather for user {user_id}")

    try:
        weather_data = await weather_api_service.get_user_weather(user_id=user_id)

        if not weather_data or not isinstance(weather_data, dict):
            return "I couldn't fetch the weather information right now."

        # # Parse fields from response
        # city = weather_data.get("city", "your location")
        # condition = (
        #     weather_data.get("weatherCondition", {})
        #     .get("description", {})
        #     .get("text", "clear")
        # )
        # temperature = weather_data.get("temperature", {}).get("degrees")
        # feels_like = weather_data.get("feelsLikeTemperature", {}).get("degrees")
        # unit = weather_data.get("temperature", {}).get("unit", "CELSIUS")
        # humidity = weather_data.get("relativeHumidity")
        # uv_index = weather_data.get("uvIndex")
        # wind_speed = weather_data.get("wind", {}).get("speed", {}).get("value")
        # wind_unit = (
        #     weather_data.get("wind", {})
        #     .get("speed", {})
        #     .get("unit", "KILOMETERS_PER_HOUR")
        # )
        # wind_direction = (
        #     weather_data.get("wind", {}).get("direction", {}).get("cardinal")
        # )

        # # Compose user-friendly summary
        # weather_summary = f"In {city}, it's currently {condition.lower()} with a temperature of {temperature:.1f}°{unit.title()}."
        # if feels_like is not None:
        #     weather_summary += f" It feels like {feels_like:.1f}°{unit.title()}."
        # if humidity is not None:
        #     weather_summary += f" Humidity is around {humidity}%."
        # if wind_speed is not None and wind_direction:
        #     weather_summary += f" Winds are coming from the {wind_direction.lower()} at {wind_speed} {wind_unit.lower()}."
        # if uv_index is not None:
        #     weather_summary += (
        #         f" The UV index is {uv_index}, so please take appropriate precautions."
        #     )

        # logger.info(f"Weather fetched: {weather_summary}")
        # # send event
        # send_event(
        #     room=ctx.session.userdata.room,
        #     action_type=ActionType.WEATHER_REPORT,
        #     message="Weather report generated for today.",
        #     data={"weatherSummary": weather_summary},
        #     status=ActionStatus.SUCCESS,
        # )
        return weather_data

    except Exception as e:
        logger.error(f"Error in get_weather tool: {e}", exc_info=True)
        raise ToolError("Sorry, I couldn't get the weather information.")


from app.services.appointment_api_service import get_appointments_by_user


async def get_yesterdays_empty_summary_appointments(ctx: UserDataContext):
    """
    Finds yesterday's appointments with empty summary and reminder_type == 'appointment'.
    Returns a list of dicts with time and sends an event for each.
    """
    if (
        not ctx.session
        or not ctx.session.userdata
        or not ctx.session.userdata.user_state
    ):
        raise ToolError("Please try to re-login")

    user_state = ctx.session.userdata.user_state
    user_id = user_state.user_id
    user_timezone = get_user_timezone(user_state)
    yesterday_local = datetime.now(user_timezone) - timedelta(days=1)
    y_date = yesterday_local.strftime("%Y-%m-%d")

    # Get all appointments for yesterday
    appointments = await get_appointments_by_user(
        user_id=user_id,
        start_date=y_date,
        end_date=y_date,
        field="summary",
        active_only=True,
        limit=20,
    )

    results = []
    agent_results = []
    for appt in appointments:
        # Get time for display
        start_time_str = appt.get("start_time")
        try:
            start_dt_utc = datetime.fromisoformat(start_time_str.replace("Z", "+00:00"))
            start_dt_local = start_dt_utc.astimezone(user_timezone)
            time_display = start_dt_local.strftime("%I:%M %p on %b %d, %Y")
        except Exception:
            time_display = str(start_time_str)

        # Build event_data structure for each past appointment
        event_data = {
            "appointmentId": str(appt.get("_id") or appt.get("id")),
            "title": appt.get("title", "Untitled"),
            "startTime": appt.get("start_time"),
            "endTime": appt.get("end_time"),
            "isRecurring": bool(appt.get("is_recurring", False)),
            "recurrenceRule": appt.get("recurrence_rule"),
        }
        results.append(event_data)
        agent_results.append(f"{appt.get('title', 'Untitled')} at {time_display}")

    return results, agent_results


@function_tool
async def morning_greeting(ctx: UserDataContext) -> str:
    """
    Greets the user in the morning.

    Args:
        ctx: The run context.

    Returns:
        A greeting message for today's weather and reminder summary.
    """
    if (
        not ctx.session
        or not ctx.session.userdata
        or not ctx.session.userdata.user_state
    ):
        raise ToolError("Internal session error.")
    print("\n\n morning_greeting called \n\n")
    # call the weather tool and list_all_reminders tool
    weather_summary = await get_weather(ctx)

    # call get_reminders with today start and end date by checking the user's timezone to convert into utc timezome than pass the start and end date for that day
    user_state = ctx.session.userdata.user_state
    user_timezone = get_user_timezone(user_state)
    today_local = datetime.now(user_timezone)
    today_utc = today_local.astimezone(timezone.utc)
    start_date = today_utc.strftime("%Y-%m-%d")
    end_date = start_date
    start_time = today_utc.strftime("%H:%M")
    end_time = "23:59"
    reminders_from_api = await get_reminders(
        user_state.user_id,
        start_date=start_date,
        end_date=end_date,
        start_time=start_time,
        end_time=end_time,
    )

    reminders_structured = []
    agent_reminders_data = []

    for r_data in reminders_from_api:
        reminder_id = str(r_data.get("_id") or r_data.get("id"))
        # convert reminder time to user's timezone and ISO string and also use here for display
        try:
            reminder_time_str = r_data.get("trigger_time").isoformat()
        except Exception as e:
            reminder_time_str = str(r_data.get("trigger_time"))

        try:
            # Ensure time is parsed and displayed in a user-friendly way, ideally in user's timezone.
            # For now, assuming trigger_time is UTC from DB.
            r_time_dt_utc = datetime.fromisoformat(
                reminder_time_str.replace("Z", "+00:00")
            )
            # If naive, set as UTC
            if r_time_dt_utc.tzinfo is None:
                r_time_dt_utc = r_time_dt_utc.replace(tzinfo=timezone.utc)
            # Only convert if user_timezone is not UTC
            if (
                (hasattr(user_timezone, "key") and user_timezone.key == "UTC")
                or (hasattr(user_timezone, "zone") and user_timezone.zone == "UTC")
                or user_timezone == timezone.utc
            ):
                r_time_dt_local = r_time_dt_utc
            else:
                r_time_dt_local = r_time_dt_utc.astimezone(user_timezone)
            r_time_display = r_time_dt_local.strftime("%I:%M %p on %b %d, %Y")
        except Exception:  # Broad except for parsing issues
            r_time_display = str(reminder_time_str)  # Fallback to raw string

        reminder = {
            "reminderId": reminder_id,
            "scheduledTime": reminder_time_str,
            "details": r_data.get("reminder_text", "Untitled Reminder"),
            "type": r_data.get("reminder_type", "general"),
            "isRecurring": bool(r_data.get("is_recurring", False)),
            "recurrenceRule": r_data.get("recurrence_rule"),
        }
        reminders_structured.append(reminder)

        agent_reminder_data = (
            f"{r_data.get('reminder_text', 'Untitled Reminder')} at {r_time_display}"
        )
        agent_reminders_data.append(agent_reminder_data)

    if not reminders_structured:
        agent_reminder_data = "You have no reminders for today."
    else:
        agent_reminder_data = "\n".join(agent_reminders_data)

    # --- Add past_appointments (yesterday's empty summary appointments) ---
    past_appointments, agent_result = await get_yesterdays_empty_summary_appointments(
        ctx
    )

    past_appointments_str = ""
    if agent_result:
        past_appointments_str = "\nPast appointments: which you have missed to added a summary/notes for yesterday are:\n"
        past_appointments_str += "\n".join(agent_result)

    greeting = f"""Good morning! {weather_summary} \n
        reminders: {agent_reminder_data} \n
        appointments: the appointments which you have missed to added a summary/notes for yesterday: {past_appointments_str}"""

    print("\n\n greeting: \n\n", greeting)
    # send this weather and reminder summary as dict with key name of weather and reminder
    greeting_message_data = {
        "weather": weather_summary,
        "reminders": reminders_structured,
        "past_appointments": past_appointments,
    }
    # send event
    await send_event(
        room=ctx.session.userdata.room,
        action_type=ActionType.MORNING_REPORT,
        message="Morning greeting sent.",
        data=greeting_message_data,
        status=ActionStatus.SUCCESS,
    )

    return greeting
