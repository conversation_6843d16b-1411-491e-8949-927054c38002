import re
from typing import Any, Dict, List, Optional, Union

from livekit.agents import ToolError, function_tool

# Assuming these tools are in different files as per your structure
from app.assistant_tools.reminders_tools import set_reminder
from app.assistant_tools.user_tools import save_user_info

# Import your application modules
from app.constants.action_types import ActionStatus, ActionType
from app.core.logger_setup import logger

# Import API service functions
# Ensure this path is correct for your project structure
from app.services import contact_api_service
from app.state.contact_state import CONTACT_ONBOARDING_DETAIL_SEQUENCE, ContactState
from app.state.session_data import UserDataContext

# Import utilities
from app.utils.event_send import send_event
from app.utils.transform_data import extract_contact_details

# We need ContactState to manage onboarding progress

# Import SessionData and UserDataContext
# Ensure this path is correct

# --- HELPER FUNCTIONS ---


def get_contact_state_from_context(ctx: UserDataContext):
    """
    Helper function to get contact state from context with robust access pattern.
    Handles both attribute and dictionary access patterns.
    """
    if not ctx.session or not ctx.session.userdata:
        raise ToolError("Internal session error.")

    # Get contact state with robust access pattern
    contact_state = None
    if hasattr(ctx.session.userdata, "contact_state"):
        contact_state = ctx.session.userdata.contact_state
    elif (
        isinstance(ctx.session.userdata, dict)
        and "session_data" in ctx.session.userdata
    ):
        contact_state = ctx.session.userdata["session_data"].contact_state
    else:
        raise ToolError("Unable to access contact state from session data.")

    if not contact_state:
        raise ToolError("Contact state not available.")

    return contact_state


def get_user_state_from_context(ctx: UserDataContext):
    """
    Helper function to get user state from context with robust access pattern.
    Handles both attribute and dictionary access patterns.
    """
    if not ctx.session or not ctx.session.userdata:
        raise ToolError("Internal session error.")

    # Get user state with robust access pattern
    user_state = None
    if hasattr(ctx.session.userdata, "user_state"):
        user_state = ctx.session.userdata.user_state
    elif (
        isinstance(ctx.session.userdata, dict)
        and "session_data" in ctx.session.userdata
    ):
        user_state = ctx.session.userdata["session_data"].user_state
    else:
        raise ToolError("Unable to access user state from session data.")

    if not user_state:
        raise ToolError("User state not available.")

    return user_state


# --- CONTACT ONBOARDING TOOLS (NEW FLOW) ---


@function_tool
async def sync_contacts(ctx: UserDataContext) -> str:
    """
    (Onboarding Step 1) Initiates contact synchronization with the user's device.

    Returns:
        "CONTACT_SYNC_REQUESTED": If the request was sent successfully.
        "TOOL_ERROR: <message>": On failure.
    """
    logger.info("Requesting contact sync.")
    # Get user state with robust access pattern
    try:
        user_state = get_user_state_from_context(ctx)
    except ToolError:
        logger.error("sync_contacts: user_state not available.")
        raise ToolError("Internal error: Cannot access user state.")

    # Handle room access with robust pattern
    room = None
    if hasattr(ctx.session.userdata, "room"):
        room = ctx.session.userdata.room
    elif isinstance(ctx.session.userdata, dict) and "room" in ctx.session.userdata:
        room = ctx.session.userdata["room"]

    if not room:
        logger.error("sync_contacts: room not available.")
        raise ToolError("Internal error: Cannot send sync request.")

    try:
        await send_event(
            room=room,
            action_type=ActionType.SYNC_CONTACT,
            message="Starting contact synchronization process.",
            data={},
            status=ActionStatus.SUCCESS,
        )
        user_state.is_synced = True  # Optimistic set
        logger.info("Contact sync request sent successfully.")
        return "CONTACT_SYNC_REQUESTED"
    except Exception as e:
        logger.error(f"Failed to send sync event: {e}", exc_info=True)
        raise ToolError(f"Failed to request contact sync: {str(e)}")


@function_tool
async def search_contact_for_onboarding(
    ctx: UserDataContext, name_query: str, current_onboarding_relation: str
) -> str:
    """
    (Onboarding Step 4) Searches for a contact by name for the current onboarding relation.
    Updates state based on search results (0, 1, or multiple).

    Args:
        name_query: The full name provided by the user.
        current_onboarding_relation: The relation being onboarded.
        ctx: The run context, containing session and user data.

    Returns:
        "CONTACT_NOT_FOUND_PROCEED_NEW": If no contact matches the name.
        "CONTACT_FOUND_SINGLE_PROCEED_DETAILS": If exactly one contact matches.
        "CONTACT_FOUND_MULTIPLE_AWAIT_CLARIFICATION": If multiple contacts match.
        "TOOL_ERROR: <message>": On failure.
    """
    # Get states with robust access pattern
    contact_state = get_contact_state_from_context(ctx)
    user_state = get_user_state_from_context(ctx)
    current_relation = current_onboarding_relation  # Get from state

    if not current_relation:
        raise ToolError("Cannot search: No current relation set for onboarding.")
    if not user_state.user_id:
        raise ToolError("Cannot search: User ID not set.")
    if not name_query or not name_query.strip():
        raise ToolError("Please provide the name to search for.")

    logger.info(
        f"Onboarding Search: User {user_state.user_id} searching for '{name_query}' for relation '{current_relation}'."
    )

    # Clear previous search state for this step
    contact_state.last_search_results = []
    contact_state.current_contact_id = None
    contact_state.current_contact_name = None
    contact_state.current_contact_first_name = None
    contact_state.current_contact_last_name = None
    contact_state.temp_details_for_current_contact = {}

    try:
        # Use the specific API search function
        contacts = await contact_api_service.search_contacts_by_name(
            user_state.user_id, name_query.strip()
        )

        if not contacts:
            logger.info(f"Onboarding Search: No contacts found for '{name_query}'.")
            contact_state.onboarding_step = (
                "ask_new_contact_details"  # LLM will ask for FN/LN/Phone
            )
            contact_state.current_contact_name = (
                name_query.strip()
            )  # Store the searched name for context
            return "CONTACT_NOT_FOUND_PROCEED_NEW", []

        elif len(contacts) == 1:
            found_contact = contacts[0]
            logger.info(
                f"Onboarding Search: Found single contact for '{name_query}': ID {found_contact.get('_id')}"
            )
            # Update state with found contact, pre-fill details
            contact_state.set_current_contact_found(
                found_contact
            )  # This sets ID, names, temp_details, and step
            # LLM confirms and proceeds to Step 5
            return "CONTACT_FOUND_SINGLE_PROCEED_DETAILS"

        else:  # Multiple contacts found
            logger.info(
                f"Onboarding Search: Found {len(contacts)} contacts matching '{name_query}'."
            )
            contact_state.set_contact_search_ambiguous(
                contacts
            )  # Stores results, sets step
            # LLM lists results and asks user to clarify
            # return "CONTACT_FOUND_MULTIPLE_AWAIT_CLARIFICATION", contacts
            return "CONTACT_FOUND_MULTIPLE_AWAIT_CLARIFICATION"

    except Exception as e:
        logger.error(
            f"Exception during onboarding contact search for '{name_query}': {e}",
            exc_info=True,
        )
        contact_state.onboarding_step = "ask_relation_name"  # Reset step on error
        raise ToolError(
            f"Sorry, I encountered an error while searching for '{name_query}': {str(e)}"
        )


@function_tool
async def get_contact_state(ctx: UserDataContext) -> str:
    """
    Fetches the latest contact state for the given ctx.
    Returns a dictionary with contact state attributes.
    It gives info about
    - last_search_results
    """
    logger.info("Contact State Tool called")
    # Stub: Replace with actual database/system query
    contact_state = get_contact_state_from_context(ctx)
    return {
        "current_contact_name": contact_state.current_contact_name,
        "current_contact_id": contact_state.current_contact_id,
        "temp_details_for_current_contact": contact_state.temp_details_for_current_contact,
        "current_relation_idx": contact_state.current_relation_idx,
        "last_search_results": contact_state.last_search_results,
        # "onboarding_step": contact_state.onboarding_step,
        # "current_onboarding_relation": contact_state.current_onboarding_relation,
        # "filtered_relations": contact_state.filtered_relations,
        # "current_relation_idx": contact_state.current_relation_idx,
    }


@function_tool
async def confirm_onboarding_contact_from_multiple(
    ctx: UserDataContext, selected_contact_identifier: str
) -> str:
    """
    (Onboarding Step 4, Case 2) Confirms which contact to use when multiple matches were found.
    Uses `contact_state.last_search_results`.

    Args:
        selected_contact_identifier: User's clarification (e.g., "number 1", name, partial email/phone).
        ctx: The run context.

    Returns:
        "CONTACT_CONFIRMED_PROCEED_DETAILS": If a single contact is identified.
        "CONTACT_CONFIRMATION_FAILED_TRY_AGAIN": If identification fails or remains ambiguous.
        "CONTACT_CONFIRMATION_ERROR_NO_RESULTS": If prior search results are missing (flow error).
    """
    # Get contact state with robust access pattern
    contact_state = get_contact_state_from_context(ctx)
    search_results = contact_state.last_search_results

    logger.info(
        f"Confirming multi-contact selection: Identifier '{selected_contact_identifier}' from {len(search_results)} options."
    )

    if not search_results:
        logger.error("Confirmation called, but no search results in contact_state.")
        contact_state.onboarding_step = "ask_relation_name"  # Reset flow
        return "CONTACT_CONFIRMATION_ERROR_NO_RESULTS"

    # --- Robust Matching Logic ---
    matched_contact: Optional[Dict[str, Any]] = None
    identifier_lower = selected_contact_identifier.lower().strip()

    # 1. Try matching by index (number)
    parsed_idx: Optional[int] = None
    if identifier_lower.startswith("number "):
        num_part = identifier_lower.split("number ")[1]
    elif identifier_lower.endswith(("st", "nd", "rd", "th")):
        num_part = "".join(filter(str.isdigit, identifier_lower))
    elif identifier_lower.isdigit():
        num_part = identifier_lower
    else:
        num_part = None

    if num_part and num_part.isdigit():
        parsed_idx = int(num_part) - 1
        if not (0 <= parsed_idx < len(search_results)):  # Check bounds
            parsed_idx = None

    # 2. Try matching by ordinal words ("first", "last")
    if parsed_idx is None:
        ordinal_map = {
            "first": 0,
            "second": 1,
            "third": 2,
            "fourth": 3,
            "fifth": 4,
            "one": 0,
            "two": 1,
            "three": 2,
            "four": 3,
            "five": 4,
            "last": len(search_results) - 1,
        }
        cleaned_identifier = re.sub(
            r"^(the|number)\s+", "", identifier_lower
        )  # Remove leading "the", "number"
        if cleaned_identifier in ordinal_map:
            parsed_idx = ordinal_map[cleaned_identifier]
            if not (0 <= parsed_idx < len(search_results)):
                parsed_idx = None  # Check bounds

    if parsed_idx is not None:
        matched_contact = search_results[parsed_idx]
        logger.info(f"Matched by index/ordinal: {parsed_idx + 1}")

    # 3. If no index match, try matching by exact name from the list
    if not matched_contact:
        for contact in search_results:
            if identifier_lower == contact.get("name", "").lower():
                matched_contact = contact
                logger.info("Matched by exact name in results.")
                break  # Assume first exact match is sufficient

    # 4. If still no match, try partial detail match (email/phone snippet) from the list
    if not matched_contact:
        for contact in search_results:
            # Check emails
            emails = contact.get("emails", [])
            for email_obj in emails:
                if identifier_lower in email_obj.get("address", "").lower():
                    matched_contact = contact
                    logger.info("Matched by email snippet in results.")
                    break
            if matched_contact:
                break

            # Check phones
            if not matched_contact:
                phones = contact.get("phone_numbers", [])
                # Clean identifier for phone matching (remove formatting)
                clean_identifier_phone = re.sub(r"[^\d]", "", identifier_lower)
                if (
                    len(clean_identifier_phone) >= 4
                ):  # Require at least 4 digits to match phone
                    for phone_obj in phones:
                        clean_phone_num = re.sub(
                            r"[^\d]", "", phone_obj.get("number", "")
                        )
                        if clean_identifier_phone in clean_phone_num:
                            matched_contact = contact
                            logger.info("Matched by phone number snippet in results.")
                            break
                if matched_contact:
                    break

    # --- Process Match Result ---
    if matched_contact:
        logger.info(
            f"Confirmed single contact: ID {matched_contact.get('_id')}, Name {matched_contact.get('name')}"
        )
        contact_state.set_current_contact_found(
            matched_contact
        )  # Updates state, pre-fills details, sets step
        contact_state.last_search_results = []  # Clear ambiguous results
        return "CONTACT_CONFIRMED_PROCEED_DETAILS"  # LLM proceeds to Step 5
    else:
        logger.warning(
            f"Could not identify contact from '{selected_contact_identifier}'. Asking user to retry."
        )
        # Keep contact_state.onboarding_step as "confirm_multiple_contacts"
        # LLM should re-prompt with the list
        return "CONTACT_CONFIRMATION_FAILED_TRY_AGAIN"


@function_tool
async def save_or_create_onboarding_contact(
    ctx: UserDataContext,
    # --- Required for NEW contacts ONLY (from Step 4, Case 3) ---
    new_contact_first_name: Optional[str] = None,
    new_contact_last_name: Optional[str] = None,
    new_contact_phone_number: Optional[str] = None,
    # --- Optional details collected for EITHER new or existing (from Step 5/6) ---
    # These are passed directly by the LLM based on user responses
    new_contact_relationship: Optional[str] = None,
    nick_name: Optional[str] = None,
    interest: Optional[str] = None,
    school: Optional[str] = None,
    job: Optional[str] = None,
    source: Optional[str] = None,
    is_emergency_contact: Optional[bool] = None,
    # Add any other optional fields collected during onboarding
    email: Optional[str] = None,  # Allow collecting email optionally
) -> str:
    """
    (Onboarding Step 7) Saves/Creates the contact for the current onboarding relation.
    Uses data collected in previous steps (stored in contact_state or passed as args).

    Args:
        ctx: The run context.
        new_contact_first_name: Required only if creating a NEW contact.
        new_contact_last_name: Optional for new contact.
        new_contact_phone_number: Required only if creating a NEW contact.
        new_contact_relationship: Required only if creating a NEW contact or updating an existing contact mention relationship.
        nick_name: Optional detail.
        interest: Optional detail.
        school: Optional detail.
        job: Optional detail.
        source: Optional detail.
        is_emergency_contact: Optional detail.
        email: Optional detail.

    Returns:
        "ONBOARDING_CONTACT_SAVED_PROCEED": On successful save/create.
        "TOOL_ERROR: <message>": On failure.
    """
    # Get states with robust access pattern
    contact_state = get_contact_state_from_context(ctx)
    user_state = get_user_state_from_context(ctx)

    if new_contact_relationship in ["father", "mother", "parents"]:
        new_contact_relationship = "parent"

    contact_id_to_update = (
        contact_state.current_contact_id
    )  # ID if updating existing, None if creating new

    current_relation = new_contact_relationship

    # Only allow use of this value during onboarding
    if user_state.is_initial_setup_complete:
        # Post-onboarding usage requires relationship to be passed
        if not current_relation:
            raise ToolError("Relationship must be specified after onboarding.")
    else:
        if not current_relation:
            raise ToolError("Cannot save contact: Onboarding relation not set.")

    # --- Consolidate all details ---
    # Start with details collected during the detail asking loop (Step 5/6)
    # These might have been pre-filled for existing contacts.
    final_details = contact_state.temp_details_for_current_contact.copy()

    # Merge/Overwrite with details passed directly to this function (LLM relaying user answers)
    provided_details = {
        "nick_name": nick_name,
        "interest": interest,
        "school": school,
        "job": job,
        "source": source,
        "is_emergency_contact": is_emergency_contact,
        "email": email,
        # Add any other optional fields here
    }
    for key, value in provided_details.items():
        if value is not None:  # Only update if LLM provided a value for this call
            final_details[key] = value

    # Add core relationship and MyHoudini flag
    final_details["relationship"] = current_relation
    final_details["is_myhoudini"] = True
    final_details["user_id"] = (
        user_state.user_id
    )  # Needed for create, maybe update API handles it

    # --- Handle Create vs Update ---
    saved_contact: Optional[Dict] = None
    action_type: ActionType = ActionType.UPDATE_CONTACT  # Default action type

    try:
        if contact_id_to_update:
            # --- UPDATE EXISTING CONTACT ---
            logger.info(
                f"Updating onboarding contact ID: {contact_id_to_update}. Relation: '{current_relation}'. Details: {final_details}"
            )
            # Remove fields API might not expect in update payload
            payload_for_update = {
                k: v for k, v in final_details.items() if k not in ["user_id", "_id"]
            }
            # Call API update function
            saved_contact = await contact_api_service.update_contact(
                contact_id_to_update, payload_for_update
            )
            if not saved_contact:
                raise ToolError("Database did not confirm the contact update.")
            action_type = ActionType.UPDATE_CONTACT

        else:
            # --- CREATE NEW CONTACT ---
            logger.info(
                f"Creating new onboarding contact. Relation: '{current_relation}'."
            )
            # Validate required fields for new contact
            if not new_contact_first_name:
                raise ToolError(
                    "Cannot create new contact: Missing required first name or phone number."
                )

            # Prepare payload for creation
            payload_for_create = final_details.copy()
            payload_for_create["first_name"] = new_contact_first_name
            payload_for_create["last_name"] = new_contact_last_name
            # Combine first/last for full name if not present
            if "name" not in payload_for_create or not payload_for_create["name"]:
                payload_for_create["name"] = new_contact_first_name + (
                    f" {new_contact_last_name}" if new_contact_last_name else ""
                )

            # Ensure phone number is structured correctly if API expects it
            # Example: Convert simple string to list of objects
            if "phone_number" not in payload_for_create and new_contact_phone_number:
                payload_for_create["phone_numbers"] = [
                    {"number": new_contact_phone_number, "type": "mobile"}
                ]  # Adapt structure as needed by API
            elif (
                "phone_number" in payload_for_create
            ):  # If already present in final_details from earlier step
                raw_phone = payload_for_create.pop("phone_number")
                if isinstance(raw_phone, str):
                    payload_for_create["phone_numbers"] = [
                        {"number": raw_phone, "type": "mobile"}
                    ]

            # Handle email structuring similarly if needed
            if "email" in payload_for_create:
                raw_email = payload_for_create.pop("email")
                if isinstance(raw_email, str):
                    payload_for_create["emails"] = [
                        {"address": raw_email, "type": "personal"}
                    ]

            # Remove any keys with None values before sending? Depends on API behavior
            payload_for_create_cleaned = {
                k: v for k, v in payload_for_create.items() if v is not None
            }

            logger.info(f"Payload for create: {payload_for_create_cleaned}")
            # Call API create function
            saved_contact = await contact_api_service.create_contact(
                payload_for_create_cleaned
            )
            if not saved_contact or not saved_contact.get("_id"):
                raise ToolError("Database did not confirm the new contact creation.")
            action_type = ActionType.ADD_CONTACT
            contact_state.current_contact_id = str(
                saved_contact.get("_id")
            )  # Store new ID in state

        # --- Post Save/Create Actions ---
        contact_name = saved_contact.get("name", "The contact")
        logger.info(
            f"Successfully saved/created onboarding contact '{contact_name}' (ID: {contact_state.current_contact_id}) for relation '{current_relation}'."
        )

        # Send event to frontend with structured event_data
        try:
            # Get the contact ID
            contact_id = saved_contact.get("_id") or contact_state.current_contact_id

            # Extract contact details using the utility function
            event_details = extract_contact_details(saved_contact)

            # Construct the event data with the specified structure
            event_data = {
                "contactId": str(contact_id),
                "details": event_details,
            }
            logger.info(f"Contact event_data: {event_data}")

            # Handle room access with robust pattern
            room = None
            if hasattr(ctx.session.userdata, "room"):
                room = ctx.session.userdata.room
            elif (
                isinstance(ctx.session.userdata, dict)
                and "room" in ctx.session.userdata
            ):
                room = ctx.session.userdata["room"]

            if room:
                await send_event(
                    room=room,
                    action_type=action_type,
                    message=f"Contact details saved for {contact_name}.",
                    data=event_data,
                    status=ActionStatus.SUCCESS,
                )
        except Exception as event_err:
            logger.error(f"Failed to send contact save/create event: {event_err}")

        # Reset state for the *next* relation (The LLM/Agent loop will handle moving index)
        contact_state.reset_for_next_relation()  # Add this method to ContactState
        contact_state.move_to_next_relation_for_onboarding()  # <<< this is critical

        return "ONBOARDING_CONTACT_SAVED_PROCEED"  # Signal success to LLM

    except ToolError as te:
        logger.error(
            f"ToolError saving/creating contact for relation '{current_relation}': {te}"
        )
        raise te  # Re-raise specific errors
    except Exception as e:
        logger.error(
            f"Unexpected error saving/creating contact for relation '{current_relation}': {e}",
            exc_info=True,
        )
        raise ToolError(
            f"Sorry, I couldn't save the contact details due to a system error: {str(e)}"
        )


@function_tool
async def skip_contact_onboarding_flow(
    ctx: UserDataContext,
    set_reminder_flag: bool,
    reminder_time_desc: Optional[str] = None,
) -> str:
    """
    Handles user skipping the contact onboarding flow. Optionally sets a reminder. Marks contact stage as complete.

    Args:
        ctx: The run context.
        set_reminder_flag: True if the user wants a reminder, False otherwise.
        reminder_time_desc: Required only if set_reminder_flag is True. e.g., "tomorrow morning".

    Returns:
        "CONTACT_ONBOARDING_SKIPPED_REMINDER_SET"
        "CONTACT_ONBOARDING_SKIPPED_NO_REMINDER"
        "TOOL_ERROR: <message>"
    """
    # Get states with robust access pattern
    contact_state = get_contact_state_from_context(ctx)
    user_state = get_user_state_from_context(ctx)

    logger.info(
        f"User requested to skip contact onboarding. Set Reminder: {set_reminder_flag}"
    )

    if set_reminder_flag:
        if not reminder_time_desc:
            raise ToolError(
                "To set a reminder, please tell me when you'd like to be reminded."
            )
        try:
            # Use the existing set_reminder tool
            reminder_text = "Complete contact setup / Sync contacts"
            await set_reminder(
                reminder_text=reminder_text,
                time_description=reminder_time_desc,
                ctx=ctx,
            )
            logger.info(
                f"Reminder set for user '{user_state.user_id}' to complete contact setup at '{reminder_time_desc}'."
            )
            # Mark stage as complete (deferred) and save user info
            contact_state.complete_onboarding_stage(user_state)  # Add this method
            await save_user_info(ctx)  # Save the updated state
            return "CONTACT_ONBOARDING_SKIPPED_REMINDER_SET"
        except ToolError as te:
            logger.error(f"Failed to set contact onboarding reminder: {te}")
            # Proceed without reminder, but inform user
            contact_state.complete_onboarding_stage(user_state)
            await save_user_info(ctx)  # Still save completion status
            raise ToolError(
                f"I couldn't set the reminder ({te}), but I've marked contact setup to continue later."
            )
        except Exception as e:
            logger.error(f"Unexpected error setting reminder: {e}", exc_info=True)
            contact_state.complete_onboarding_stage(user_state)
            await save_user_info(ctx)  # Still save completion status
            raise ToolError(
                "Sorry, an unexpected error occurred while setting the reminder. I've marked contact setup to continue later."
            )
    else:
        # No reminder requested
        contact_state.complete_onboarding_stage(user_state)  # Mark as complete/skipped
        await save_user_info(ctx)  # Save state
        return "CONTACT_ONBOARDING_SKIPPED_NO_REMINDER"


@function_tool
async def complete_contact_onboarding_flow(
    ctx: UserDataContext,
) -> str:
    """
    Handles user completing the contact onboarding flow.
    Marks the contact onboarding stage as complete and saves user state.

    Args:
        ctx: The run context.

    Returns:
        "CONTACT_ONBOARDING_COMPLETED"
        "TOOL_ERROR: <message>"
    """
    # Get states with robust access pattern
    contact_state = get_contact_state_from_context(ctx)
    user_state = get_user_state_from_context(ctx)

    try:
        logger.info(f"User completed the contact onboarding flow.")

        # Mark the onboarding stage as complete
        contact_state.complete_onboarding_stage(user_state)

        # Save the updated user state
        await save_user_info(ctx)

        return "CONTACT_ONBOARDING_COMPLETED"

    except Exception as e:
        logger.error(
            f"Unexpected error completing contact onboarding: {e}", exc_info=True
        )
        raise ToolError("Sorry, an error occurred while finalizing contact onboarding.")


# --- GENERAL Contact Tools (Post-Onboarding) ---
# Keep add_new_contact and update_existing_contact as they were previously defined
# They handle general use cases outside the specific onboarding flow.


@function_tool
async def add_new_contact(
    ctx: UserDataContext,
    name: str,
    relationship: Optional[str] = None,
    phone_number: Optional[str] = None,
    email: Optional[str] = None,
    address: Optional[str] = None,
    nick_name: Optional[str] = None,
    school: Optional[str] = None,
    job: Optional[str] = None,
    interest: Optional[str] = None,
    hobbies: Optional[str] = None,
    is_emergency_contact: Optional[bool] = None,
    source: Optional[str] = None,
    birthday: Optional[str] = None,
) -> str:
    """
    Adds a new contact *after* the initial onboarding is complete.
    Args: name, ctx, and optional details.
    """
    # (Implementation from previous correct answer - Seems OK)
    logger.info(f"Adding new contact (post-onboarding): {name}")
    if (
        not ctx.session
        or not ctx.session.userdata
        or not ctx.session.userdata.user_state
    ):
        raise ToolError("Internal session error.")
    user_id = ctx.session.userdata.user_state.user_id
    if not user_id:
        raise ToolError("User ID not found.")
    if not name or not name.strip():
        raise ToolError("Please provide the name.")

    if relationship in ["father", "mother", "parents"]:
        relationship = "parent"

    contact_data = {
        "user_id": user_id,
        "name": name.strip(),
        "relationship": relationship,
        "phone_numbers": (
            [{"number": phone_number, "type": "mobile"}] if phone_number else []
        ),
        "emails": [{"address": email, "type": "personal"}] if email else [],
        "address": address,
        "nick_name": nick_name,
        "school": school,
        "job": job,
        "interest": interest,
        "hobbies": hobbies,
        "is_emergency_contact": is_emergency_contact,
        "birthday": birthday,
        "source": source or "agent_added",
        "is_myhoudini": True,
    }
    contact_data_cleaned = {
        k: v
        for k, v in contact_data.items()
        if v is not None and not (isinstance(v, list) and not v)
    }

    try:
        created_contact = await contact_api_service.create_contact(contact_data_cleaned)
        if created_contact and created_contact.get("_id"):
            # Create the structured event_data format
            contact_id = created_contact.get("_id")

            # Extract contact details using the utility function
            event_details = extract_contact_details(created_contact)

            # Construct the event data with the specified structure
            event_data = {
                "contactId": str(contact_id),
                "details": event_details,
            }
            logger.info(f"Contact event_data: {event_data}")

            await send_event(
                room=ctx.session.userdata.room,
                action_type=ActionType.ADD_CONTACT,
                message=f"Contact '{created_contact.get('name', name)}' added.",
                data=event_data,
                status=ActionStatus.SUCCESS,
            )
            return f"Okay, I've added {created_contact.get('name', name)}."
        else:
            raise ToolError("Failed to add contact: Database did not confirm.")
    except Exception as e:
        logger.error(f"Error adding contact {name}: {e}", exc_info=True)
        raise ToolError(f"Sorry, unexpected error adding {name}: {str(e)}")


@function_tool
async def update_existing_contact(
    ctx: UserDataContext,
    name: str,
    new_name: Optional[str] = None,
    job: Optional[str] = None,
    phone_number: Optional[str] = None,
    school: Optional[str] = None,
    nick_name: Optional[str] = None,
    is_emergency_contact: Optional[bool] = None,
    relationship: Optional[str] = None,
    address: Optional[str] = None,
    email: Optional[str] = None,
    interest: Optional[str] = None,
    hobbies: Optional[str] = None,
    source: Optional[str] = None,
    birthday: Optional[str] = None,
) -> str:
    """
    Updates specific details for an existing contact *after* onboarding. Searches by name.
    Args: name (current name), ctx, and optional fields to update (e.g., new_name, job).
    """
    # (Implementation from previous correct answer - Relies on search_contacts_by_name and update_contact API)
    logger.info(f"Attempting to update contact named: {name}")
    if (
        not ctx.session
        or not ctx.session.userdata
        or not ctx.session.userdata.user_state
    ):
        raise ToolError("Internal session error.")
    user_id = ctx.session.userdata.user_state.user_id
    if not user_id:
        raise ToolError("User ID not found.")
    if not name or not name.strip():
        raise ToolError("Please tell me the contact name to update.")

    if relationship in ["father", "mother", "parents"]:
        relationship = "parent"

    # 1. Find contact (using general search API)
    try:
        contacts = await contact_api_service.search_contacts_by_name(
            user_id, name.strip()
        )
        if not contacts:
            raise ToolError(
                f"Couldn't find '{name}'. Add as new contact or try another name?"
            )
        elif len(contacts) > 1:
            ctx.session.userdata.current_contact_search_results = (
                contacts  # Store for clarification
            )
            options = [
                f"{i+1}. {c.get('name', 'Unknown')}(Ref: ...{str(c.get('_id', ''))[-4:]})"
                for i, c in enumerate(contacts[:5])
            ]
            # LLM must clarify
            return f"CONTACT_UPDATE_MULTIPLE_FOUND: Found multiple '{name}': {'; '.join(options)}. Which one?"
        else:
            contact_to_update = contacts[0]
            contact_id = str(contact_to_update.get("_id"))
            logger.info(
                f"Found contact to update: {contact_to_update.get('name')} (ID: {contact_id})"
            )
    except Exception as e:
        logger.error(f"Error searching contact '{name}' for update: {e}", exc_info=True)
        raise ToolError(f"Error finding '{name}': {str(e)}")

    if not relationship and "relationship" in contact_to_update:
        relationship = contact_to_update["relationship"]

    # 2. Prepare update payload (Only include fields passed to this tool)
    update_payload = {
        "name": new_name,
        "job": job,
        "school": school,
        "nick_name": nick_name,
        "is_emergency_contact": is_emergency_contact,
        "relationship": relationship,
        "address": address,
        "interest": interest,
        "hobbies": hobbies,
        "source": source,
        "birthday": birthday,
        # Handle phone/email simple updates (API needs to merge lists or replace)
        "phone_numbers": (
            [{"number": phone_number, "type": "mobile"}]
            if phone_number is not None
            else None
        ),
        "emails": (
            [{"address": email, "type": "personal"}] if email is not None else None
        ),
    }
    update_data_cleaned = {k: v for k, v in update_payload.items() if v is not None}

    if not update_data_cleaned:
        raise ToolError(f"You asked to update {name}, but didn't specify changes.")

    # 3. Call update API
    try:
        updated_contact = await contact_api_service.update_contact(
            contact_id, update_data_cleaned
        )
        if updated_contact:
            final_name = updated_contact.get("name", name)

            # Create the structured event_data format
            contact_id = updated_contact.get("_id") or contact_id

            # Extract contact details using the utility function
            event_details = extract_contact_details(updated_contact)

            # If we have the original contact data, update with any fields that might be preserved
            if contact_to_update:
                event_details.update(extract_contact_details(contact_to_update))

            # Construct the event data with the specified structure
            event_data = {
                "contactId": str(contact_id),
                "details": event_details,
            }
            logger.info(f"Contact event_data: {event_data}")

            await send_event(
                room=ctx.session.userdata.room,
                action_type=ActionType.UPDATE_CONTACT,
                message=f"Contact '{final_name}' updated.",
                data=event_data,
                status=ActionStatus.SUCCESS,
            )
            return f"Okay, I've updated details for {final_name}."
        else:
            raise ToolError(f"Failed to update {name}: Database did not confirm.")
    except Exception as e:
        logger.error(
            f"Error updating contact {name} (ID: {contact_id}): {e}", exc_info=True
        )
        raise ToolError(f"Sorry, unexpected error updating {name}: {str(e)}")
