import asyncio
from datetime import date, datetime, timedelta, timezone
from typing import Any, Dict, List, Optional, Union

from livekit.agents import ToolError, function_tool, llm
from livekit.plugins import openai  # For TTS type hint if needed

from app.constants.action_types import ActionType
from app.constants.env_constants import GOOGLE_API_KEY
from app.core.logger_setup import logger
from app.providers.tts.factory import (
    get_available_voices,
)  # Assuming this exists or add it
from app.providers.tts.voice_mappings import (  # Keep mapping logic
    get_display_name,
    get_voice_id,
)

# Import API service functions
from app.services import user_api_service
from app.state.session_data import UserDataContext
from app.utils.event_send import send_event

# Import SessionData and UserDataContext from the new module

# --- USER ONBOARDING & MANAGEMENT TOOLS ---


@function_tool
async def set_user_name(
    ctx: UserDataContext,
    name: Optional[str] = None,
    first_name: Optional[str] = None,
    last_name: Optional[str] = None,
) -> str:
    """
    Sets the user's full name and updates the database. Used during onboarding.
    Can accept either a full name or separate first and last names.

    Args:
        name: The user's full name (if providing as a single string).
        first_name: The user's first name (if providing separately).
        last_name: The user's last name (if providing separately).
        ctx: The run context.
    """
    # Determine the full name from the provided parameters
    if name:
        full_name = name.strip()
    elif first_name or last_name:
        # Combine first and last name
        first = (first_name or "").strip()
        last = (last_name or "").strip()
        full_name = f"{first} {last}".strip()
    else:
        raise ToolError("Please provide either a full name or first/last name.")

    if not full_name:
        raise ToolError("Please provide a valid name.")

    logger.info(f"Setting user name to: {full_name}")
    user_state = ctx.session.userdata.user_state
    user_state.name = full_name
    if "name" not in user_state.onboarding_stage_completed:
        user_state.onboarding_stage_completed.append("name")

    try:
        await user_api_service.save_or_update_user(
            user_data={
                "name": full_name,
                "preferred_name": full_name,
                "onboarding_stage_completed": user_state.onboarding_stage_completed,
                "last_interacted_at": datetime.now(timezone.utc).isoformat(),
            },
            user_id=user_state.user_id,
        )
        return f"Name set to {full_name}."
    except Exception as e:
        logger.error(f"Failed to save user name {full_name}: {e}", exc_info=True)
        # Propagate error to LLM so it can inform user
        raise ToolError(
            f"Sorry, I couldn't save your name right now due to a system error: {str(e)}"
        )


@function_tool
async def set_preferred_name(ctx: UserDataContext, name: str) -> str:
    """
    Sets the user's preferred name. Used during onboarding.
    Args:
        name: The user's preferred name.
        ctx: The run context.
    """
    logger.info(f"Setting preferred name to: {name}")
    ctx.session.userdata.user_state.preferred_name = name
    await user_api_service.save_or_update_user(
        user_data={
            "preferred_name": name,
            "last_interacted_at": datetime.now(timezone.utc).isoformat(),
        },
        user_id=ctx.session.userdata.user_state.user_id,
    )
    return f"Preferred Name set to {name}."


@function_tool
async def set_phone_number(ctx: UserDataContext, phone_number: str) -> str:
    """
    Sets the user's phone number. Used during onboarding.
    Args:
        phone_number: The user's phone number.
        ctx: The run context.
    """
    logger.info(f"Setting phone number to: {phone_number}")
    ctx.session.userdata.user_state.phone_number = phone_number
    await user_api_service.save_or_update_user(
        user_data={
            "phone_number": phone_number,
            "last_interacted_at": datetime.now(timezone.utc).isoformat(),
        },
        user_id=ctx.session.userdata.user_state.user_id,
    )
    return f"Phone number set to {phone_number}."


@function_tool
async def get_user_state(ctx: UserDataContext) -> str:
    """
    Fetches the latest user state for the given ctx.
    Returns a dictionary with user state attributes.
    """
    # Stub: Replace with actual database/system query
    user_state = ctx.session.userdata.user_state
    return {
        "name": user_state.name,
        "time_zone": user_state.time_zone,
        "is_synced": user_state.is_synced,
        "preferred_name": user_state.preferred_name,
        "daily_updates_time": user_state.daily_updates_time,
        "is_initial_setup_complete": user_state.is_initial_setup_complete,
        "onboarding_stage_completed": user_state.onboarding_stage_completed,
        "is_initial_setup_complete": user_state.is_initial_setup_complete,
    }


@function_tool
async def set_location_details(
    ctx: UserDataContext, city: str, state: str, zip_code: str, verified: bool
) -> str:
    """
    Sets the user's city and state. Used during onboarding.
    Args:
        city: The user's city.
        state: The user's state.
        zip_code: Thes user's zip_code.
        verified: city, state and zip verified using `validate_zip_code` tool
        ctx: The run context.
    """
    logger.info(f"Setting city to: {city} and state to: {state}")
    ctx.session.userdata.user_state.city = city
    ctx.session.userdata.user_state.state = state
    ctx.session.userdata.user_state.zip_code = zip_code
    if verified:
        await user_api_service.save_or_update_user(
            user_data={
                "city": city,
                "state": state,
                "zip_code": zip_code,
                "last_interacted_at": datetime.now(timezone.utc).isoformat(),
            },
            user_id=ctx.session.userdata.user_state.user_id,
        )
        return (
            f"City set to {city} , state set to {state} and zip code set to {zip_code}."
        )
    else:
        return "Details are not verified , verify them using validate_zip_code tool"


@function_tool
async def set_birth_day_and_month(
    ctx: UserDataContext, birth_day: str, birth_month: str
) -> str:
    """
    Sets the user's birthday. Used during onboarding.
    Args:
        birth_day: The user's birth day (1-31).
        birth_month: The user's birth month (always pass full name of month like January, February, etc.).
        ctx: The run context.
    """
    logger.info(f"Setting birthday to: {birth_day} {birth_month}")
    ctx.session.userdata.user_state.birth_day = birth_day
    ctx.session.userdata.user_state.birth_month = birth_month
    await user_api_service.save_or_update_user(
        user_data={
            "birth_day": birth_day,
            "birth_month": birth_month,
            "last_interacted_at": datetime.now(timezone.utc).isoformat(),
        },
        user_id=ctx.session.userdata.user_state.user_id,
    )
    return f"Birthday set to {birth_day} {birth_month}."


@function_tool
async def play_all_voice_samples(ctx: UserDataContext) -> str:
    """
    Plays all available voice samples for the user to hear and choose from.
    Whatever the command related to voice, like list or name or play the voice the just call this function to play the voice samples.
    Args:
        ctx: The run context.
    """
    logger.info("Playing all voice samples")
    user_state = ctx.session.userdata.user_state

    original_voice_id = user_state.voice_name
    provider = user_state.tts_provider or "openai"  # Default to openai if not set

    available_voices = get_available_voices(provider)
    if not available_voices:
        logger.warning(
            f"No voices listed for provider '{provider}'. Cannot play samples."
        )
        return f"Sorry, I don't have a list of voices for the '{provider}' provider."

    try:
        if hasattr(ctx.session, "say") and callable(ctx.session.say):
            await ctx.session.say(
                "Listen to my different voices, and let me know which one you prefer.",
                allow_interruptions=True,
            )
            await asyncio.sleep(1.5)  # Give user time to hear
        else:
            logger.error(
                "ctx.session.say is not available or not callable for initial message"
            )
    except Exception as e:
        logger.error(f"Failed to say initial message: {e}")

    for voice_id in available_voices:
        display_name = get_display_name(provider, voice_id) or voice_id.capitalize()

        try:
            # Check if TTS is available and supports update_options
            if ctx.session.tts and hasattr(ctx.session.tts, "update_options"):
                # Temporarily update TTS voice for the sample
                # Assuming openai.TTS plugin used and it supports update_options
                if isinstance(
                    ctx.session.tts, openai.TTS
                ):  # Check specific plugin type
                    try:
                        ctx.session.tts.update_options(voice=voice_id)
                        logger.info(
                            f"Temporarily changed voice to {voice_id} ({display_name}) for sample."
                        )
                    except Exception as e:
                        logger.error(f"Failed to update voice to {voice_id}: {e}")
                        # Continue with current voice
                else:  # Fallback if not openai.TTS or doesn't support update_options
                    logger.warning(
                        f"TTS plugin {type(ctx.session.tts)} may not support dynamic voice updates. Sample for {voice_id} might use default voice."
                    )
            else:
                logger.warning(
                    f"Session TTS is not set or doesn't support dynamic updates. Cannot play sample for {voice_id}."
                )

            # Try to say the sample text, even if voice update failed
            if hasattr(ctx.session, "say") and callable(ctx.session.say):
                try:
                    await ctx.session.say(
                        f"This is my {display_name} voice.", allow_interruptions=False
                    )
                    await asyncio.sleep(1.5)
                except Exception as e:
                    logger.error(f"Failed to say sample for voice {voice_id}: {e}")
            else:
                logger.error("ctx.session.say is not available or not callable")
        except Exception as e:
            logger.error(f"Failed to play sample for voice {voice_id}: {e}")

    # Restore original voice
    try:
        if (
            ctx.session.tts
            and hasattr(ctx.session.tts, "update_options")
            and isinstance(ctx.session.tts, openai.TTS)
            and original_voice_id
        ):
            try:
                ctx.session.tts.update_options(voice=original_voice_id)
                logger.info(f"Restored original voice: {original_voice_id}")
            except Exception as e:
                logger.error(
                    f"Failed to restore original voice {original_voice_id}: {e}"
                )
    except Exception as e:
        logger.error(f"Error during voice restoration: {e}")

    return "Played samples for all available voices."


@function_tool
async def set_voice(ctx: UserDataContext, voice: str) -> str:
    """
    Sets the assistant's voice for the current session and persists it to the DB.
    Accepts both internal voice IDs (e.g., 'nova') and known display names (e.g., 'Sandra').
    Args:
        voice: The voice ID or display name.
        ctx: The run context.
    """
    logger.info(f"Attempting to set preferred voice to: {voice}")
    user_state = ctx.session.userdata.user_state
    provider = user_state.tts_provider or "openai"

    # Handle special cases for default voice
    if voice.lower() in [
        "default",
        "current",
        "this one",
        "keep this",
        "stay with this",
    ]:
        # If user wants to keep current voice, just mark onboarding as complete
        if user_state.voice_name:
            resolved_voice_id = user_state.voice_name
            logger.info(f"User chose to keep current voice: {resolved_voice_id}")
        else:
            # If no voice is set, use a default
            resolved_voice_id = "alloy"  # Default OpenAI voice
            logger.info(f"Setting default voice: {resolved_voice_id}")
    else:
        # Resolve display name to internal ID if necessary
        resolved_voice_id = get_voice_id(provider, voice) or voice

    available_voices = get_available_voices(provider)
    if resolved_voice_id not in available_voices:
        logger.error(
            f"Invalid voice ID '{resolved_voice_id}' for provider '{provider}'. Available: {available_voices}"
        )
        display_names_available = [
            get_display_name(provider, v_id) or v_id.capitalize()
            for v_id in available_voices
        ]
        # Don't raise an error, just use a default voice instead
        logger.warning(
            f"Using default voice 'alloy' instead of invalid voice '{voice}'"
        )
        resolved_voice_id = "alloy"  # Default to alloy if voice not found

    user_state.voice_name = resolved_voice_id  # Update state

    # Update the live TTS instance in the session
    try:
        # First, update the voice in the user_state
        user_state.voice_name = resolved_voice_id
        logger.info(f"Updated user_state.voice_name to {resolved_voice_id}")

        # Then try to update the TTS instance if available
        if ctx.session.tts and hasattr(ctx.session.tts, "update_options"):
            if isinstance(ctx.session.tts, openai.TTS):  # Example for OpenAI TTS
                try:
                    # Try to recreate the TTS instance with the new voice
                    try:
                        old_tts = ctx.session.tts
                        model = getattr(old_tts, "model", "tts-1")
                        api_key = getattr(old_tts, "api_key", None)

                        # Create a new TTS instance with the updated voice
                        new_tts = openai.TTS(
                            model=model, voice=resolved_voice_id, api_key=api_key
                        )

                        # Replace the TTS instance in the session
                        ctx.session.tts = new_tts
                        logger.info(
                            f"Recreated TTS instance with voice {resolved_voice_id}"
                        )
                    except Exception as recreate_e:
                        logger.error(f"Failed to recreate TTS instance: {recreate_e}")
                        # Fall back to update_options

                    # Also try the update_options method
                    await ctx.session.tts.update_options(voice=resolved_voice_id)
                    logger.info(f"Updated TTS options to voice {resolved_voice_id}")
                    logger.info(
                        f"Session TTS voice updated to {resolved_voice_id} for provider {provider}."
                    )
                except Exception as e:
                    logger.error(
                        f"Failed to update session TTS voice to {resolved_voice_id}: {e}"
                    )
                    # Don't raise ToolError here, as preference is set, but live change failed
            else:
                logger.warning(
                    f"Session TTS plugin {type(ctx.session.tts)} might not support dynamic voice updates or isn't the expected type."
                )
        else:
            logger.warning(
                f"Session TTS is not set or does not support dynamic updates."
            )
    except Exception as e:
        logger.error(f"Error during voice update: {e}", exc_info=True)
        # Continue with saving the preference even if the live update fails

    # Persist the change and mark voice onboarding as complete
    if "voice" not in user_state.onboarding_stage_completed:
        user_state.onboarding_stage_completed.append("voice")

    try:
        user_data = {
            "voice_name": resolved_voice_id,
            "onboarding_stage_completed": user_state.onboarding_stage_completed,
            "last_interacted_at": datetime.now(timezone.utc).isoformat(),
        }
        logger.info(f"Saving voice preference to database: {user_data}")

        result = await user_api_service.save_or_update_user(
            user_id=user_state.user_id,
            user_data=user_data,
        )

        # Send event to frontend
        display_name = (
            get_display_name(provider, resolved_voice_id)
            or resolved_voice_id.capitalize()
        )
        event_data = {
            "voiceId": resolved_voice_id,
            "displayName": display_name,
            "provider": provider,
        }
        message = f"Voice changed to {display_name}"
        await send_event(
            room=ctx.session.userdata.room,
            action_type=ActionType.SELECT_VOICE,
            message=message,
            data=event_data,
        )

        logger.info(f"Voice preference saved to database: {resolved_voice_id}")
        logger.info(f"Database update result: {result}")
    except Exception as e:
        logger.error(
            f"Failed to save preferred voice {resolved_voice_id}: {e}", exc_info=True
        )
        # Don't raise ToolError here, as preference set in state, TTS *might* be updated live

    display_name_confirm = (
        get_display_name(provider, resolved_voice_id) or resolved_voice_id.capitalize()
    )
    return f"Okay, I've set my voice to {display_name_confirm}."


@function_tool
async def check_onboarding_status(ctx: UserDataContext) -> str:
    """
    Checks the current onboarding status and determines the next step.
    This function should be called at the beginning of a conversation to determine where to start.

    Args:
        ctx: The run context.
    """
    user_state = ctx.session.userdata.user_state
    logger.info(f"Checking onboarding status: {user_state.onboarding_stage_completed}")

    if not user_state.onboarding_stage_completed:
        # No stages completed, start with name
        return "START_NAME_ONBOARDING"

    if (
        "name" in user_state.onboarding_stage_completed
        and "voice" not in user_state.onboarding_stage_completed
    ):
        # Name completed, start voice onboarding
        return "START_VOICE_ONBOARDING"

    if (
        "name" in user_state.onboarding_stage_completed
        and "voice" in user_state.onboarding_stage_completed
        and "contact" not in user_state.onboarding_stage_completed
    ):
        # Name and voice completed, start contact onboarding
        return "START_CONTACT_ONBOARDING"

    if (
        "name" in user_state.onboarding_stage_completed
        and "voice" in user_state.onboarding_stage_completed
        and "contact" in user_state.onboarding_stage_completed
    ):
        # All stages completed, mark onboarding as complete if not already
        if not user_state.is_initial_setup_complete:
            user_state.is_initial_setup_complete = True
            try:
                await user_api_service.save_or_update_user(
                    user_id=user_state.user_id,
                    user_data={
                        "is_initial_setup_complete": True,
                        "last_interacted_at": datetime.now(timezone.utc).isoformat(),
                    },
                )
                logger.info("Marked initial setup as complete")
            except Exception as e:
                logger.error(
                    f"Failed to mark initial setup as complete: {e}", exc_info=True
                )

        return "ONBOARDING_COMPLETE"

    # Default case - determine based on what's missing
    missing_stages = []
    if "name" not in user_state.onboarding_stage_completed:
        missing_stages.append("name")
    if "voice" not in user_state.onboarding_stage_completed:
        missing_stages.append("voice")
    if "contact" not in user_state.onboarding_stage_completed:
        missing_stages.append("contact")

    logger.info(f"Missing onboarding stages: {missing_stages}")
    if "name" in missing_stages:
        return "START_NAME_ONBOARDING"
    elif "voice" in missing_stages:
        return "START_VOICE_ONBOARDING"
    elif "contact" in missing_stages:
        return "START_CONTACT_ONBOARDING"
    else:
        return "ONBOARDING_COMPLETE"


@function_tool
async def start_voice_onboarding(ctx: UserDataContext) -> str:
    """
    Starts the voice onboarding flow with a friendly introduction.
    This function should be called when the user is ready to select a voice.

    Args:
        ctx: The run context.
    """
    logger.info("Starting voice onboarding flow with introduction")

    try:
        # Say the introduction message
        if hasattr(ctx.session, "say") and callable(ctx.session.say):
            try:
                await ctx.session.say(
                    "This is my mira voice, but I have some others. Would you like to hear them?",
                    allow_interruptions=True,
                )
                logger.info("Voice onboarding introduction played successfully")
            except Exception as e:
                logger.error(f"Failed to say voice onboarding introduction: {e}")
        else:
            logger.error(
                "ctx.session.say is not available or not callable for voice onboarding"
            )
    except Exception as e:
        logger.error(f"Error during voice onboarding introduction: {e}")

    # Return a message for the LLM to continue the conversation
    return "Voice onboarding started. Ask the user if they want to hear different voice options."


# @function_tool
# async def handle_voice_selection(
#     ctx: UserDataContext, voice_choice: Optional[str] = None
# ) -> str:
#     """
#     Handles the complete voice selection flow, including playing samples and setting the chosen voice.
#     If voice_choice is provided, sets that voice directly. Otherwise, plays samples once only and waits for user choice.

#     Args:
#         ctx: The run context.
#         voice_choice: (Optional) The voice ID or display name to set directly.
#     """
#     logger.info(f"Handling voice selection flow, initial choice: {voice_choice}")

#     if voice_choice:
#         try:
#             # User already specified a voice, set it directly
#             logger.info(f"Setting voice to user's choice: {voice_choice}")

#             # # Map common voice names to OpenAI voice IDs
#             # voice_mapping = {
#             #     "alloy": "alloy",
#             #     "echo": "echo",
#             #     "fable": "fable",
#             #     "onyx": "onyx",
#             #     "nova": "nova",
#             #     "shimmer": "nova",  # Common alternative names
#             #     "sky": "nova",
#             #     "ember": "nova",
#             #     "james": "echo",
#             #     "diana": "nova",
#             #     "sandra": "nova",
#             #     "michael": "onyx",
#             #     "john": "onyx",
#             #     "david": "echo",
#             #     "female": "nova",
#             #     "male": "echo",
#             #     "woman": "nova",
#             #     "man": "echo",
#             #     "girl": "nova",
#             #     "boy": "echo",
#             #     "default": "alloy",
#             # }
#             from config import VOICE_MAPPING

#             voice_mapping = VOICE_MAPPING

#             # Convert voice choice to lowercase for case-insensitive matching
#             voice_lower = voice_choice.lower()

#             # Use the mapping if available, otherwise use the original choice
#             resolved_voice = voice_mapping.get(voice_lower, voice_choice)
#             logger.info(f"Resolved voice '{voice_choice}' to '{resolved_voice}'")

#             # Set the voice
#             await set_voice(resolved_voice, ctx)

#             # Make sure voice is marked as completed in onboarding
#             user_state = ctx.session.userdata.user_state
#             if "voice" not in user_state.onboarding_stage_completed:
#                 user_state.onboarding_stage_completed.append("voice")
#                 logger.info(
#                     f"Added 'voice' to onboarding_stage_completed: {user_state.onboarding_stage_completed}"
#                 )

#                 # Save the updated onboarding status
#                 try:
#                     user_data = {
#                         "onboarding_stage_completed": user_state.onboarding_stage_completed,
#                         "last_interacted_at": datetime.now(timezone.utc).isoformat(),
#                     }
#                     logger.info(f"Saving updated onboarding status: {user_data}")

#                     result = await user_api_service.save_or_update_user(
#                         user_id=user_state.user_id,
#                         user_data=user_data,
#                     )

#                     logger.info(f"Onboarding status update result: {result}")
#                 except Exception as e:
#                     logger.error(
#                         f"Failed to save onboarding status: {e}", exc_info=True
#                     )

#             # Provide a simple confirmation message
#             try:
#                 if hasattr(ctx.session, "say") and callable(ctx.session.say):
#                     await ctx.session.say(
#                         "Great, I'll use this voice from now on. You can always change it later.",
#                         allow_interruptions=True,
#                     )
#                     logger.info("Voice confirmation message played successfully")
#                 else:
#                     logger.error(
#                         "ctx.session.say is not available for voice confirmation"
#                     )
#             except Exception as e:
#                 logger.error(f"Error during voice confirmation: {e}")

#             return "Voice set successfully. Onboarding for voice is complete."
#         except Exception as e:
#             logger.error(f"Error during voice selection: {e}", exc_info=True)
#             return f"Failed to set voice: {str(e)}"
#     else:
#         # Play voice samples first
#         await play_all_voice_samples(ctx)
#         # The LLM will need to ask the user which voice they prefer
#         # and then call set_voice with the user's choice
#         return "Voice samples played. Please ask the user which voice they prefer."


@function_tool(
    description="Saves current user profile information like name, preferred name, voice preference, and onboarding completion status to the database."
)
async def save_user_info(ctx: UserDataContext) -> str:
    """Saves key user information (name, preferred_name, voice_name, onboarding_stage_completed, is_initial_setup_complete, daily_updates_time) to the database."""
    logger.info("Saving user info...")
    user_state = ctx.session.userdata.user_state

    if not user_state.user_id:
        logger.error("Cannot save user info: user_id is missing.")
        raise ToolError(
            "I couldn't save your information because your user ID is missing."
        )

    # Make sure voice onboarding is marked as complete if we have a voice set
    if user_state.voice_name and "voice" not in user_state.onboarding_stage_completed:
        user_state.onboarding_stage_completed.append("voice")
        logger.info(
            f"Marked voice onboarding as complete with voice: {user_state.voice_name}"
        )

    data_to_save = {
        "name": user_state.name,
        "preferred_name": user_state.preferred_name,
        "voice_name": user_state.voice_name,
        "onboarding_stage_completed": user_state.onboarding_stage_completed,
        "is_initial_setup_complete": user_state.is_initial_setup_complete,
        # Ensure this is part of UserState
        "daily_updates_time": user_state.daily_updates_time,
        "last_interacted_at": datetime.now(timezone.utc).isoformat(),
    }

    try:
        await user_api_service.save_or_update_user(
            user_id=user_state.user_id,
            user_data=data_to_save,
        )
        logger.info(f"User info saved successfully for user: {user_state.user_id}")
        return "User information saved."
    except Exception as e:
        logger.error(
            f"Failed to save user info for user {user_state.user_id}: {e}",
            exc_info=True,
        )
        raise ToolError(
            f"Sorry, I couldn't save your information right now due to a system error: {str(e)}"
        )


@function_tool
async def set_daily_updates_time(ctx: UserDataContext, time_str: str) -> str:
    """
    Sets the user's usual daily-updates time preference and saves it.
    Args:
        time_str: The user's daily-updates time (e.g., '7 am', '08:00').
        ctx: The run context.
    """
    logger.info(f"Setting daily-updates time based on input: '{time_str}'")
    user_state = ctx.session.userdata.user_state
    try:
        # More robust parsing needed here. For simplicity, assume LLM provides H:M or H AM/PM
        # Example: try to parse common formats
        parsed_time_obj = None
        fmts = ["%I:%M%p", "%H:%M", "%I%p"]  # Add more as needed
        for fmt in fmts:
            try:
                parsed_time_obj = datetime.strptime(
                    time_str.replace(" ", "").upper(), fmt
                )
                break
            except ValueError:
                continue

        if not parsed_time_obj:
            raise ValueError("Could not parse time format.")

        parsed_time_24hr = parsed_time_obj.strftime("%H:%M")

    except ValueError:
        logger.warning(f"Could not parse daily-updates time: {time_str}")
        raise ToolError(
            "Sorry, I couldn't understand that time format. Please use HH:MM or, for example, '7 am'."
        )

    user_state.daily_updates_time = parsed_time_24hr
    try:
        await user_api_service.save_or_update_user(
            user_id=user_state.user_id,
            user_data={
                "daily_updates_time": parsed_time_24hr,
                "last_interacted_at": datetime.now(timezone.utc).isoformat(),
            },
        )
        # Confirm in user-friendly format
        return f"Okay, I've set your daily-updates time to {parsed_time_obj.strftime('%I:%M %p')}."
    except Exception as e:
        logger.error(f"Failed to save daily-updates time: {e}", exc_info=True)
        raise ToolError(
            f"Sorry, I couldn't save your daily-updates time right now due to a system error: {str(e)}"
        )


import requests


@function_tool
async def validate_zip_code(zip_code: str, city: str, state: str) -> dict:
    """
    Validates whether the ZIP code matches the provided city and state using Google Maps Geocoding API.
    Returns detailed comparison results and canonical values.

    Args:
        zip_code (str): ZIP code entered by user
        city (str): City entered by user
        state (str): State entered by user

    Returns:
        dict: {
            "status": "ZIP_VALIDATION_SUCCESS" | "ZIP_VALIDATION_PARTIAL" | "ZIP_VALIDATION_FAILED" | "ZIP_VALIDATION_ERROR",
            "input": { "city": str, "state": str, "zip_code": str },
            "canonical": { "city": str, "state": str, "zip_code": str },
            "mismatches": [ "city", "state", "zip_code" ] (if any),
            "message": "..." (brief summary for agent)
        }
    """
    try:
        api_key = GOOGLE_API_KEY
        address = f"{city}, {state} {zip_code}"
        url = f"https://maps.googleapis.com/maps/api/geocode/json?address={address}&key={api_key}"
        response = requests.get(url)
        data = response.json()

        if not data.get("results"):
            return {
                "status": "ZIP_VALIDATION_FAILED",
                "input": {"city": city, "state": state, "zip_code": zip_code},
                "canonical": {},
                "mismatches": [],
                "message": "No results found for the given location.",
            }

        components = data["results"][0]["address_components"]
        canonical = {"city": None, "state": None, "zip_code": None}

        for comp in components:
            if "locality" in comp["types"]:
                canonical["city"] = comp["long_name"]
            elif "administrative_area_level_1" in comp["types"]:
                canonical["state"] = comp["short_name"]
            elif "postal_code" in comp["types"]:
                canonical["zip_code"] = comp["long_name"]

        mismatches = []
        if canonical["city"] and canonical["city"].lower() != city.lower():
            mismatches.append("city")
        if canonical["state"] and canonical["state"].lower() != state.lower():
            mismatches.append("state")
        if canonical["zip_code"] and canonical["zip_code"] != zip_code:
            mismatches.append("zip_code")

        status = (
            "ZIP_VALIDATION_SUCCESS" if not mismatches else "ZIP_VALIDATION_PARTIAL"
        )

        return {
            "status": status,
            "input": {"city": city, "state": state, "zip_code": zip_code},
            "canonical": canonical,
            "mismatches": mismatches,
            "message": (
                "Validation complete with mismatches."
                if mismatches
                else "All inputs are valid."
            ),
        }

    except Exception as e:
        return {
            "status": "ZIP_VALIDATION_ERROR",
            "input": {"city": city, "state": state, "zip_code": zip_code},
            "canonical": {},
            "mismatches": [],
            "message": str(e),
        }
