"""
Action Types and Status Enums for the MyHoudini Assistant.

This module defines the enums used for system events and actions in the MyHoudini assistant.
These enums are used to standardize communication between the backend and frontend.
"""

from enum import Enum

# make this list as enum ['parent', 'spouse', 'child', 'sibling', 'greatgrandchild', 'grandchild', 'doctor', 'caregiver', 'friend', 'neighbor']


class RelationType(str, Enum):
    parent = "parent"
    spouse = "spouse"
    child = "child"
    sibling = "sibling"
    greatgrandchild = "greatgrandchild"
    grandchild = "grandchild"
    doctor = "doctor"
    caregiver = "caregiver"
    friend = "friend"
    neighbor = "neighbor"


class ActionType(str, Enum):
    """
    Enum defining the types of actions that can be performed by the assistant.

    These action types are used as topics for WebRTC data channels and to categorize
    different types of user interactions.
    """

    UNDEFINED = "undefined"
    SELECT_VOICE = "select_voice"  # For voice selection/change
    SYNC_CONTACT = "sync_contacts"  # For contact synchronization
    ADD_CONTACT = "add_contact"
    UPDATE_CONTACT = "update_contact"  # For voice selection/change
    SET_REMINDER = "set_reminder"  # Covers meditation, appointment, general
    UPDATE_REMINDER = "update_reminder"  # Covers meditation, appointment, general
    CREATE_APPOINTMENT = "create_appointment"  # Calendar + reminder
    UPDATE_APPOINTMENT = "update_appointment"  # Calendar + reminder
    MANAGE_NOTES = "manage_notes"  # Could be create, view, update, delete
    MANAGE_MEETING_CONVERSATION = (
        "manage_meeting_conversation"  # For meeting conversations/notes
    )
    WEATHER_REPORT = "weather_report"  # Specific recurring weather report
    MORNING_REPORT = "morning_report"  # General morning report
    LIST_REMINDERS = "list_reminders"  # For listing reminders
    REDIRECT_USER = "redirect_user"  # For redirection like internet support
    FETCH_NEWS = "fetch_news"


class ActionStatus(str, Enum):
    """
    Enum defining the possible status values for actions.

    These status values indicate the outcome of an action and are used to
    determine how the frontend should respond.
    """

    SUCCESS = "success"
    FAILURE = "failure"
    PARTIAL_SUCCESS = "partial_success"
    REQUIRES_CLARIFICATION = "requires_clarification"


class ErrorCode(str, Enum):
    """
    Enum defining error codes for failed actions.

    These error codes provide more specific information about why an action failed
    and can be used for error handling and user feedback.
    """

    CONTACT_NOT_FOUND = "contact_not_found"
    CALENDAR_API_UNAVAILABLE = "calendar_api_unavailable"
    MISSING_PARAMETER = "missing_parameter"
    INVALID_TIME_FORMAT = "invalid_time_format"
    AUTHENTICATION_FAILED = "authentication_failed"
    PERMISSION_DENIED = "permission_denied"
    SERVICE_UNAVAILABLE = "service_unavailable"
    NETWORK_ERROR = "network_error"
    UNKNOWN_ERROR = "unknown_error"


class FlowTrigger(str, Enum):
    MORNING = ("MORNING",)
    CONTACT = ("CONTACT",)
    REMINDER = ("REMINDER",)
    APPOINTMENT = ("APPOINTMENT",)
    GENERAL = "GENERAL"
    ONBOARDING = "ONBOARDING"
