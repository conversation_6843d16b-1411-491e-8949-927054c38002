# env_constants.py
# Load all environment variables from .env.local and provide them as constants

import os
from typing import Optional

from dotenv import load_dotenv

# Load environment variables from the .env file (if present)
load_dotenv(override=True)

# LiveKit credentials
LIVEKIT_API_KEY: str = os.getenv("LIVEKIT_API_KEY", "")
LIVEKIT_API_SECRET: str = os.getenv("LIVEKIT_API_SECRET", "")
LIVEKIT_URL: str = os.getenv("LIVEKIT_URL", "")

# OpenAI credentials
OPENAI_API_KEY: str = os.getenv("OPENAI_API_KEY", "")
OPENAI_MODEL_NAME: str = os.getenv("OPENAI_MODEL", "gpt-4o")

# Deepgram credentials
DEEPGRAM_API_KEY: str = os.getenv("DEEPGRAM_API_KEY", "")

#Google Credentials
GEMINI_API_KEY: str = os.getenv("GEMINI_API_KEY", "")

# MongoDB connection settings
MONGO_URI: str = os.getenv("MONGO_URI", "")
MONGODB_DB_NAME: str = os.getenv("MONGODB_DB_NAME", "myhoudini")

# Default voice settings
DEFAULT_VOICE: str = os.getenv("DEFAULT_VOICE", "alloy")
AVAILABLE_VOICES: list[str] = ["alloy", "echo", "fable", "onyx", "nova"]

# Default reminder settings
DEFAULT_REMINDER_UNIT: str = os.getenv("DEFAULT_REMINDER_UNIT", "minute")
DEFAULT_REMINDER_UNITS: list[str] = ["minute", "hour", "day", "week"]

# Default appointment notification time (in minutes)
DEFAULT_APPOINTMENT_NOTIFY_BEFORE: int = int(
    os.getenv("DEFAULT_APPOINTMENT_NOTIFY_BEFORE", "60")
)

# Server settings
TOKEN_SERVER_PORT: int = int(os.getenv("TOKEN_SERVER_PORT", "5000"))
TOKEN_SERVER_HOST: str = os.getenv("TOKEN_SERVER_HOST", "0.0.0.0")

# Application settings
DEBUG_MODE: bool = os.getenv("DEBUG_MODE", "False").lower() in ["true", "1", "t", "yes"]
LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")

API_SERVER_ENDPOINT: str = os.getenv("API_SERVER_ENDPOINT", "http://localhost:8000")

API_ENDPOINT_KEY: str = os.getenv("API_ENDPOINT_KEY", "")

GOOGLE_API_KEY: str = os.getenv("GOOGLE_API_KEY", "")


# Helper function to validate environment variables
def validate_env_vars() -> list[str]:
    """Validate that all required environment variables are set.

    Returns:
        A list of missing environment variables.
    """
    required_vars = [
        "LIVEKIT_API_KEY",
        "LIVEKIT_API_SECRET",
        "OPENAI_API_KEY",
        "DEEPGRAM_API_KEY",
        "MONGO_URI",
    ]

    missing_vars = [var for var in required_vars if not os.getenv(var)]
    return missing_vars


# Check for missing environment variables
missing_vars = validate_env_vars()
if missing_vars:
    print(
        f"Warning: The following required environment variables are missing: {', '.join(missing_vars)}"
    )
