import re
from collections.abc import AsyncIterable

from livekit import rtc
from livekit.agents import Agent, ChatContext, llm

from app.assistant_tools.appointement_tools import (
    add_contact_for_appointment,
    apply_appointment_updates,
    confirm_contact_for_appointment,
    create_appointment,
    delete_appointment,
    find_appointment,
    find_contact_for_appointment,
    handle_appointment_update_flow,
    list_all_appointments,
    select_appointment_for_update,
)
from app.assistant_tools.contact_tools import add_new_contact, update_existing_contact
from app.assistant_tools.reminders_tools import (
    delete_reminder_tool,
    find_reminder,
    list_all_reminders,
    set_reminder,
    update_existing_reminder,
)
from app.assistant_tools.user_tools import (
    get_user_state,
    save_user_info,
    set_daily_updates_time,
    set_voice,
)
from app.assistant_tools.weather_tools import get_weather
from app.core.logger_setup import logger
from app.core.pronunciation_rules import STATIC_PRONUNCIATIONS
from app.prompts.general_prompt import general_prompt


class GeneralAgent(Agent):
    def __init__(self):
        super().__init__(
            instructions=general_prompt,
            tools=[
                get_user_state,
                set_reminder,
                find_reminder,
                update_existing_reminder,
                list_all_reminders,
                delete_reminder_tool,
                create_appointment,
                find_appointment,
                apply_appointment_updates,
                select_appointment_for_update,
                list_all_appointments,
                delete_appointment,
                handle_appointment_update_flow,
                add_new_contact,
                update_existing_contact,
                get_weather,
                set_voice,
                set_daily_updates_time,
                save_user_info,
                add_contact_for_appointment,
                confirm_contact_for_appointment,
                find_contact_for_appointment,
            ],
        )
        self.logger = logger
        self.pronunciations = STATIC_PRONUNCIATIONS.copy()
        sorted_terms = sorted(self.pronunciations.keys(), key=len, reverse=True)
        self.compiled_pronunciation_rules = []
        for term in sorted_terms:
            pattern = re.compile(rf"\b{re.escape(term)}\b", re.IGNORECASE)
            ssml_replacement = self.pronunciations[term]
            self.compiled_pronunciation_rules.append((pattern, ssml_replacement))
        self.session = None

    async def start(self, ctx):
        """
        Called when the agent starts. Sends the initial message for general flow.
        """
        self.session = ctx

        # Get initial message from session data
        initial_message = ctx.userdata.get("initial_message")
        if initial_message:
            self.logger.info(f"GeneralAgent sending initial message: {initial_message}")
            await self.say(initial_message, allow_interruptions=True)

    async def tts_node(
        self, text_stream: AsyncIterable[str], model_settings: dict
    ) -> AsyncIterable[rtc.AudioFrame]:
        self.logger.debug("Entering tts_node")
        full_text = "".join([chunk async for chunk in text_stream])
        self.logger.debug(f"tts_node received text: {full_text}")
        modified_text = full_text
        ssml_used = False

        for pattern_regex, ssml_replacement in self.compiled_pronunciation_rules:
            if pattern_regex.search(modified_text):
                modified_text = pattern_regex.sub(ssml_replacement, modified_text)
                ssml_used = True

        if ssml_used:
            if not modified_text.strip().lower().startswith("<speak>"):
                modified_text = f"<speak>{modified_text}</speak>"
            self.logger.debug(f"TTS input (SSML processed): {modified_text}")
        else:
            self.logger.debug(f"TTS input (plain text): {modified_text}")

        async def single_text_chunk_iterable(text: str) -> AsyncIterable[str]:
            yield text

        async for frame in Agent.default.tts_node(
            self, single_text_chunk_iterable(modified_text), model_settings
        ):
            self.logger.debug("Yielding audio frame from tts_node")
            yield frame
