import re
from collections.abc import AsyncIterable

from livekit import rtc
from livekit.agents import Agent, ChatContext, llm

from app.assistant_tools.appointement_tools import (
    handle_appointment_update_flow,
    list_all_appointments,
)
from app.assistant_tools.reminders_tools import find_reminder, list_all_reminders
from app.assistant_tools.user_tools import get_user_state
from app.assistant_tools.weather_tools import morning_greeting
from app.core.logger_setup import logger
from app.core.pronunciation_rules import STATIC_PRONUNCIATIONS
from app.prompts.morning_prompt import morning_prompt


class MorningAgent(Agent):
    def __init__(self):
        super().__init__(
            instructions=morning_prompt,
            tools=[
                get_user_state,
                morning_greeting,
                find_reminder,
                list_all_reminders,
                list_all_appointments,
                handle_appointment_update_flow,
            ],
        )
        self.logger = logger
        self.pronunciations = STATIC_PRONUNCIATIONS.copy()
        sorted_terms = sorted(self.pronunciations.keys(), key=len, reverse=True)
        self.compiled_pronunciation_rules = []
        for term in sorted_terms:
            pattern = re.compile(rf"\b{re.escape(term)}\b", re.IGNORECASE)
            ssml_replacement = self.pronunciations[term]
            self.compiled_pronunciation_rules.append((pattern, ssml_replacement))
        self.session = None

    async def start(self, ctx):
        """
        Called when the agent starts. Sends the initial morning greeting and calls morning_greeting tool.
        """
        self.session = ctx

        # Get initial message from session data
        initial_message = ctx.userdata.get("initial_message")
        if initial_message:
            self.logger.info(f"MorningAgent sending initial message: {initial_message}")
            await self.say(initial_message, allow_interruptions=True)

            # For morning agent, also call the morning_greeting tool to get weather and reminders
            try:
                from app.assistant_tools.weather_tools import morning_greeting
                from app.state.session_data import UserDataContext

                # Create a context for the tool call
                tool_ctx = UserDataContext(session=ctx)
                await morning_greeting(tool_ctx)
            except Exception as e:
                self.logger.error(
                    f"Error calling morning_greeting tool: {e}", exc_info=True
                )

    async def tts_node(
        self, text_stream: AsyncIterable[str], model_settings: dict
    ) -> AsyncIterable[rtc.AudioFrame]:
        full_text = "".join([chunk async for chunk in text_stream])
        modified_text = full_text
        ssml_used = False

        for pattern_regex, ssml_replacement in self.compiled_pronunciation_rules:
            if pattern_regex.search(modified_text):
                modified_text = pattern_regex.sub(ssml_replacement, modified_text)
                ssml_used = True

        if ssml_used:
            if not modified_text.strip().lower().startswith("<speak>"):
                modified_text = f"<speak>{modified_text}</speak>"
            self.logger.debug(f"TTS input (SSML processed): {modified_text}")
        else:
            self.logger.debug(f"TTS input (plain text): {modified_text}")

        async def single_text_chunk_iterable(text: str) -> AsyncIterable[str]:
            yield text

        async for frame in Agent.default.tts_node(
            self, single_text_chunk_iterable(modified_text), model_settings
        ):
            yield frame

    # async def llm_node(
    #     self, chat_ctx: ChatContext, tools: list, model_settings: dict
    # ) -> llm.StopResponse:
    #     user_state = self.session.userdata.user_state
    #     preferred_name = user_state.preferred_name or user_state.name or "there"

    #     formatted_prompt = morning_prompt.replace("[preferred_name]", preferred_name)
    #     current_turn_chat_ctx = ChatContext()
    #     system_message_exists = False

    #     for item in chat_ctx.items:
    #         if item.role == llm.ChatRole.SYSTEM:
    #             current_turn_chat_ctx.messages.append(
    #                 llm.ChatMessage(role=llm.ChatRole.SYSTEM, content=formatted_prompt)
    #             )
    #             system_message_exists = True
    #         else:
    #             current_turn_chat_ctx.messages.append(item)

    #     if not system_message_exists:
    #         current_turn_chat_ctx.messages.insert(
    #             0, llm.ChatMessage(role=llm.ChatRole.SYSTEM, content=formatted_prompt)
    #         )

    #     current_turn_chat_ctx.truncate(max_items=20)
    #     return await super().llm_node(current_turn_chat_ctx, tools, model_settings)
