import re
from collections.abc import AsyncIterable

from livekit import rtc
from livekit.agents import Agent, ChatContext, ModelSettings, llm

from app.assistant_tools.contact_tools import (
    complete_contact_onboarding_flow,
    confirm_onboarding_contact_from_multiple,
    get_contact_state,
    save_or_create_onboarding_contact,
    search_contact_for_onboarding,
    skip_contact_onboarding_flow,
    sync_contacts,
)
from app.assistant_tools.user_tools import (
    get_user_state,
    play_all_voice_samples,
    save_user_info,
    set_birth_day_and_month,
    set_location_details,
    set_phone_number,
    set_preferred_name,
    set_user_name,
    set_voice,
    validate_zip_code,
)
from app.core.logger_setup import logger
from app.core.pronunciation_rules import STATIC_PRONUNCIATIONS
from app.prompts.onboarding_prompt import onboarding_prompt


class OnboardingAgent(Agent):
    def __init__(self):
        super().__init__(
            instructions=onboarding_prompt,
            tools=[
                get_user_state,
                get_contact_state,
                set_user_name,
                set_preferred_name,
                set_phone_number,
                set_location_details,
                set_birth_day_and_month,
                set_voice,
                play_all_voice_samples,
                save_user_info,
                sync_contacts,
                search_contact_for_onboarding,
                confirm_onboarding_contact_from_multiple,
                save_or_create_onboarding_contact,
                complete_contact_onboarding_flow,
                skip_contact_onboarding_flow,
                validate_zip_code,
            ],
        )
        self.logger = logger
        self.pronunciations = STATIC_PRONUNCIATIONS.copy()
        sorted_terms = sorted(self.pronunciations.keys(), key=len, reverse=True)
        self.compiled_pronunciation_rules = []
        for term in sorted_terms:
            pattern = re.compile(rf"\b{re.escape(term)}\b", re.IGNORECASE)
            ssml_replacement = self.pronunciations[term]
            self.compiled_pronunciation_rules.append((pattern, ssml_replacement))

    async def tts_node(
        self, text_stream: AsyncIterable[str], model_settings: dict
    ) -> AsyncIterable[rtc.AudioFrame]:
        full_text = "".join([chunk async for chunk in text_stream])
        modified_text = full_text
        ssml_used = False

        for pattern_regex, ssml_replacement in self.compiled_pronunciation_rules:
            if pattern_regex.search(modified_text):
                modified_text = pattern_regex.sub(ssml_replacement, modified_text)
                ssml_used = True

        if ssml_used:
            if not modified_text.strip().lower().startswith("<speak>"):
                modified_text = f"<speak>{modified_text}</speak>"
            self.logger.debug(f"TTS input (SSML processed): {modified_text}")
        else:
            self.logger.debug(f"TTS input (plain text): {modified_text}")

        async def single_text_chunk_iterable(text: str) -> AsyncIterable[str]:
            yield text

        async for frame in Agent.default.tts_node(
            self, single_text_chunk_iterable(modified_text), model_settings
        ):
            yield frame

    # async def llm_node(
    #     self, chat_ctx: ChatContext, tools: list, model_settings: dict
    # ) -> llm.StopResponse:
    #     user_state = self.session.userdata.user_state
    #     contact_state = self.session.userdata.contact_state
    #     preferred_name = user_state.preferred_name or user_state.name or "there"

    #     current_onboarding_relation = (
    #         contact_state.filtered_relations[contact_state.current_relation_idx]
    #         if contact_state.current_relation_idx >= 0
    #         and contact_state.current_relation_idx
    #         < len(contact_state.filtered_relations)
    #         else "all_contacts_set_up"
    #     )

    #     formatted_prompt = onboarding_prompt.format(
    #         current_onboarding_relation=current_onboarding_relation
    #     )
    #     current_turn_chat_ctx = ChatContext()
    #     system_message_exists = False

    #     for item in chat_ctx.items:
    #         if item.role == llm.ChatRole.SYSTEM:
    #             current_turn_chat_ctx.messages.append(
    #                 llm.ChatMessage(role=llm.ChatRole.SYSTEM, content=formatted_prompt)
    #             )
    #             system_message_exists = True
    #         else:
    #             current_turn_chat_ctx.messages.append(item)

    #     if not system_message_exists:
    #         current_turn_chat_ctx.messages.insert(
    #             0, llm.ChatMessage(role=llm.ChatRole.SYSTEM, content=formatted_prompt)
    #         )

    #     current_turn_chat_ctx.truncate(max_items=20)
    #     return await super().llm_node(current_turn_chat_ctx, tools, model_settings)
