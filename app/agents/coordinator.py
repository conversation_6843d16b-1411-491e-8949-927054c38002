import asyncio
import json

from aiolimiter import AsyncLimiter
from livekit import rtc
from livekit.agents import (
    AgentSession,
    AutoSubscribe,
    RoomInputOptions,
    RoomOutputOptions,
)
from livekit.plugins import deepgram, google, noise_cancellation, openai, silero
from livekit.plugins.turn_detector.multilingual import MultilingualModel
from tenacity import retry, stop_after_attempt, wait_exponential

from app.agents.general_agent import GeneralAgent
from app.agents.morning_agents import MorningAgent
from app.agents.onboarding_agent import OnboardingAgent
from app.assistant_tools.user_tools import get_user_state
from app.constants.action_types import FlowTrigger
from app.constants.env_constants import (
    DEEPGRAM_API_KEY,
    DEFAULT_VOICE,
    GEMINI_API_KEY,
    OPENAI_API_KEY,
)
from app.core.logger_setup import logger
from app.prompts.general_prompt import general_prompt
from app.prompts.morning_prompt import morning_prompt
from app.prompts.onboarding_prompt import onboarding_prompt
from app.services.contact_api_service import get_myhoudini_contacts_relationships
from app.services.user_api_service import get_user
from app.state.contact_state import CONTACT_UPDATE_RELATIONS
from app.state.session_data import SessionData


class Coordinator:
    def __init__(self):
        self.logger = logger
        self.rate_limiter = AsyncLimiter(max_rate=30, time_period=60)  # 30 requests/min

    @retry(
        stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10)
    )
    async def fetch_user_state(self, user_id):
        return await get_user(user_id)

    def get_initial_message(self, session_data):
        """
        Determine the initial message based on onboarding stages and flow type.
        """
        user_state = session_data.user_state
        onboarding_completed = user_state.onboarding_stage_completed or []

        # Check if user is in onboarding flow
        if not user_state.is_initial_setup_complete:
            # Name stage
            if "name" not in onboarding_completed:
                return "Hello! I'm your personal assistant. To get started, could you please tell me your name?"

            # Voice stage
            elif "voice" not in onboarding_completed:
                return "This is my regular voice, but I have some others. Would you like to hear them?"

            # Contact stage
            elif "contact" not in onboarding_completed:
                preferred_name = user_state.preferred_name or user_state.name or "there"
                return f"Great {preferred_name}! Now let's add some important contacts to your profile. I can help you add family members, doctors, caregivers, and other important people. Would you like to start adding contacts?"

            # Fallback for onboarding
            else:
                return "Let's continue setting up your profile. Is there anything specific you'd like to configure?"

        # Morning flow
        elif user_state.current_flow == FlowTrigger.MORNING.value:
            preferred_name = user_state.preferred_name or user_state.name or "there"
            return (
                f"Good morning {preferred_name}! Let me give you your morning update."
            )

        # General flow
        else:
            preferred_name = user_state.preferred_name or user_state.name or "there"
            return f"Hello {preferred_name}! How can I help you today?"

    async def start_agent(
        self,
        ctx,
        agent_class,
        prompt,
        session_data,
        stt_plugin,
        llm_plugin,
        tts_plugin,
        vad_plugin,
        turn_detector,
    ):
        # Get initial message based on onboarding stage and flow
        initial_message = self.get_initial_message(session_data)

        # Store initial message in session data for agent access
        session_data_dict = {
            "session_data": session_data,
            "initial_message": initial_message,
        }

        agent_session = AgentSession(
            stt=stt_plugin,
            llm=llm_plugin,
            tts=tts_plugin,
            vad=vad_plugin,
            turn_detection=turn_detector,
            userdata=session_data_dict,
            allow_interruptions=True,
            min_interruption_duration=0.8,
        )
        agent = agent_class()

        # Start the agent session
        await agent_session.start(
            agent=agent,
            room=ctx.room,
            room_input_options=RoomInputOptions(
                audio_enabled=True, video_enabled=False
            ),
            room_output_options=RoomOutputOptions(
                audio_enabled=True, transcription_enabled=True
            ),
        )

        # Call the agent's start method to send initial message
        if hasattr(agent, "start"):
            try:
                await agent.start(agent_session)
                self.logger.info(f"Agent start method called successfully")
            except Exception as e:
                self.logger.error(
                    f"Error calling agent start method: {e}", exc_info=True
                )

    async def route(self, ctx):
        user_id = self.get_user_id_from_room(ctx)
        session_data = SessionData(room=ctx.room, user_id=user_id)

        # Fetch initial metadata
        metadata = json.loads(self.get_user_metadata_from_room(ctx) or "{}")
        session_data.user_state.current_flow = metadata.get(
            "event_type", FlowTrigger.GENERAL.value
        )
        session_data.user_state.current_flow_id = metadata.get("event_id")

        # Initialize plugins
        try:
            stt_plugin = deepgram.STT(api_key=DEEPGRAM_API_KEY, language="en-US")
            llm_plugin = google.LLM(api_key=GEMINI_API_KEY)
            tts_plugin = openai.TTS(
                model="tts-1",
                voice=session_data.user_state.voice_name or DEFAULT_VOICE,
                api_key=OPENAI_API_KEY,
            )
            vad_plugin = silero.VAD.load(
                min_speech_duration=0.2,
                min_silence_duration=0.8,
                activation_threshold=0.7,
            )
            turn_detector = MultilingualModel()
            self.logger.info("Plugins initialized.")
        except Exception as e:
            self.logger.error(f"Failed to initialize plugins: {e}", exc_info=True)
            await ctx.room.disconnect()
            return

        # Fetch user state via API
        try:
            if user_id:
                user_data = await self.fetch_user_state(user_id)
                if user_data and isinstance(user_data, dict):
                    user_data = user_data.get("data", user_data)
                    session_data.user_state.load_from_dict(user_data)
                    if not user_data.get("onboarding_stage_completed"):
                        session_data.user_state.onboarding_stage_completed = []
                    if not user_data.get("time_zone"):
                        session_data.user_state.time_zone = "UTC"
                    if user_data.get("voice"):
                        session_data.user_state.voice_name = user_data.get(
                            "voice_name", DEFAULT_VOICE
                        )
                    session_data.user_state.is_initial_setup_complete = bool(
                        user_data.get("is_onboarded", False)
                    )
                else:
                    self.logger.warning(f"No user data found for user_id: {user_id}")
                    # Set defaults for new user
                    session_data.user_state.onboarding_stage_completed = []
                    session_data.user_state.time_zone = "UTC"
                    session_data.user_state.is_initial_setup_complete = False
            else:
                self.logger.warning("No user_id available, using default settings")
                # Set defaults for new user
                session_data.user_state.onboarding_stage_completed = []
                session_data.user_state.time_zone = "UTC"
                session_data.user_state.is_initial_setup_complete = False

                # Load contact relations
                try:
                    contact_relation_list = await get_myhoudini_contacts_relationships(
                        user_id
                    )
                    relations_to_onboard = [
                        r
                        for r in CONTACT_UPDATE_RELATIONS
                        if r not in (contact_relation_list or [])
                    ]
                    session_data.contact_state.relations_to_onboard = (
                        CONTACT_UPDATE_RELATIONS
                    )
                    session_data.contact_state.filtered_relations = relations_to_onboard
                    session_data.filtered_contact_relations_for_prompt = ", ".join(
                        relations_to_onboard
                    )
                except Exception as e:
                    self.logger.error(
                        f"Error fetching contact relations: {e}", exc_info=True
                    )
                    session_data.contact_state.filtered_relations = (
                        CONTACT_UPDATE_RELATIONS
                    )
                    session_data.filtered_contact_relations_for_prompt = ", ".join(
                        CONTACT_UPDATE_RELATIONS
                    )
        except Exception as e:
            self.logger.error(f"Failed to fetch user state: {e}", exc_info=True)
            session_data.user_state.voice_name = DEFAULT_VOICE

        # Route to appropriate agent
        user_state = session_data.user_state
        if not user_state.is_initial_setup_complete:
            self.logger.info("Starting OnboardingAgent")
            await self.start_agent(
                ctx,
                OnboardingAgent,
                onboarding_prompt,
                session_data,
                stt_plugin,
                llm_plugin,
                tts_plugin,
                vad_plugin,
                turn_detector,
            )
        elif user_state.current_flow == FlowTrigger.MORNING.value:
            self.logger.info("Starting MorningAgent")
            await self.start_agent(
                ctx,
                MorningAgent,
                morning_prompt,
                session_data,
                stt_plugin,
                llm_plugin,
                tts_plugin,
                vad_plugin,
                turn_detector,
            )
        else:
            self.logger.info("Starting GeneralAgent")
            await self.start_agent(
                ctx,
                GeneralAgent,
                general_prompt,
                session_data,
                stt_plugin,
                llm_plugin,
                tts_plugin,
                vad_plugin,
                turn_detector,
            )

        # Metadata listener for flow changes
        @ctx.room.on("participant_metadata_changed")
        def _on_metadata_change(participant: rtc.RemoteParticipant):
            if participant.identity == user_id:
                metadata = json.loads(participant.metadata or "{}")
                new_flow = metadata.get("event_type", FlowTrigger.GENERAL.value)
                if new_flow != session_data.user_state.current_flow:
                    session_data.user_state.current_flow = new_flow
                    session_data.user_state.current_flow_id = metadata.get("event_id")
                    self.logger.info(f"Switching to flow: {new_flow}")
                    asyncio.create_task(self.route(ctx))

    def get_user_id_from_room(self, ctx):
        # First try to get from remote participants
        for _sid, participant in ctx.room.remote_participants.items():
            if participant.kind != rtc.ParticipantKind.PARTICIPANT_KIND_AGENT:
                self.logger.info(f"Found participant: {participant.identity}")
                return participant.identity

        # If no participants yet, try to extract from room name
        # Room names are often in format like "komal6" where the number might be user ID
        # or the entire room name might be the user ID
        room_name = ctx.room.name
        self.logger.info(
            f"No participants found, trying to extract user ID from room name: {room_name}"
        )

        # For now, let's use the room name as user ID and handle the API error gracefully
        # This will be updated when we know the exact room naming convention
        if room_name and room_name != "komal6":  # Skip test room names
            return room_name

        # For development/testing, return a default user ID
        return "683014fe463301cd0fd556c1"  # Use the user ID from the logs

    def get_user_metadata_from_room(self, ctx):
        for _sid, participant in ctx.room.remote_participants.items():
            if participant.kind != rtc.ParticipantKind.PARTICIPANT_KIND_AGENT:
                return participant.metadata
        return None
