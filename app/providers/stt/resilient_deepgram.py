"""
Resilient Deepgram STT provider that can automatically recover from connection errors.
"""

import asyncio
import logging
from typing import Any, Callable, Dict, List, Optional, Union

from livekit.agents._exceptions import APIStatusError
from livekit.plugins.deepgram.stt import STT as DeepgramSTT

logger = logging.getLogger(__name__)


class ResilientDeepgramSTT(DeepgramSTT):
    """
    A wrapper around the Deepgram STT provider that can automatically recover from connection errors.
    """

    def __init__(self, max_retries: int = 3, retry_delay: float = 2.0, **kwargs):
        """
        Initialize the resilient Deepgram STT provider.

        Args:
            max_retries: Maximum number of retries when a connection error occurs
            retry_delay: Delay in seconds between retries
            **kwargs: Additional arguments to pass to the Deepgram STT provider
        """
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.current_retries = 0
        self.error_handlers: List[Callable] = []
        self.recovery_handlers: List[Callable] = []
        
        # Initialize the Deepgram STT provider
        try:
            super().__init__(**kwargs)
            logger.info("Resilient Deepgram STT provider initialized successfully")
        except Exception as e:
            logger.error(f"Error initializing Deepgram STT provider: {e}")
            # Try again with a delay
            asyncio.sleep(self.retry_delay)
            super().__init__(**kwargs)
    
    def on_error(self, handler: Callable):
        """
        Register an error handler.
        
        Args:
            handler: The error handler function
        """
        self.error_handlers.append(handler)
        return handler
    
    def on_recovery(self, handler: Callable):
        """
        Register a recovery handler.
        
        Args:
            handler: The recovery handler function
        """
        self.recovery_handlers.append(handler)
        return handler
    
    async def _handle_error(self, error: Exception):
        """
        Handle an error by notifying error handlers.
        
        Args:
            error: The error that occurred
        """
        for handler in self.error_handlers:
            try:
                if asyncio.iscoroutinefunction(handler):
                    await handler(error)
                else:
                    handler(error)
            except Exception as e:
                logger.error(f"Error in error handler: {e}")
    
    async def _handle_recovery(self):
        """
        Handle recovery by notifying recovery handlers.
        """
        for handler in self.recovery_handlers:
            try:
                if asyncio.iscoroutinefunction(handler):
                    await handler()
                else:
                    handler()
            except Exception as e:
                logger.error(f"Error in recovery handler: {e}")
    
    async def _try_reconnect(self):
        """
        Try to reconnect to the Deepgram service.
        
        Returns:
            True if reconnection was successful, False otherwise
        """
        if self.current_retries >= self.max_retries:
            logger.error(f"Maximum retries ({self.max_retries}) reached, giving up")
            return False
        
        self.current_retries += 1
        logger.info(f"Attempting to reconnect to Deepgram (attempt {self.current_retries}/{self.max_retries})")
        
        try:
            # Close the existing connection if it exists
            if hasattr(self, '_ws') and self._ws:
                try:
                    await self._ws.close()
                except Exception as e:
                    logger.warning(f"Error closing WebSocket connection: {e}")
            
            # Reinitialize the connection
            await self._connect()
            
            # Reset retry counter on success
            self.current_retries = 0
            logger.info("Successfully reconnected to Deepgram")
            
            # Notify recovery handlers
            await self._handle_recovery()
            
            return True
        except Exception as e:
            logger.error(f"Error reconnecting to Deepgram: {e}")
            # Wait before the next retry
            await asyncio.sleep(self.retry_delay)
            return False
    
    async def _connect(self):
        """
        Connect to the Deepgram service with retry logic.
        """
        try:
            await super()._connect()
        except Exception as e:
            logger.error(f"Error connecting to Deepgram: {e}")
            await self._handle_error(e)
            await self._try_reconnect()
    
    async def recv_task(self):
        """
        Override the receive task to add error handling and automatic reconnection.
        """
        while True:
            try:
                await super().recv_task()
            except APIStatusError as e:
                if "deepgram connection closed" in str(e).lower():
                    logger.warning(f"Deepgram connection closed unexpectedly: {e}")
                    await self._handle_error(e)
                    
                    # Try to reconnect
                    if await self._try_reconnect():
                        continue
                    else:
                        # If reconnection failed after max retries, re-raise the error
                        raise
                else:
                    # For other API status errors, re-raise
                    raise
            except Exception as e:
                logger.error(f"Unexpected error in recv_task: {e}")
                await self._handle_error(e)
                
                # Try to reconnect for any error
                if await self._try_reconnect():
                    continue
                else:
                    # If reconnection failed after max retries, re-raise the error
                    raise


def create_resilient_stt(**kwargs) -> ResilientDeepgramSTT:
    """
    Create a resilient Deepgram STT instance.
    
    Args:
        **kwargs: Additional arguments to pass to the ResilientDeepgramSTT constructor
    
    Returns:
        A resilient Deepgram STT instance
    """
    try:
        return ResilientDeepgramSTT(**kwargs)
    except Exception as e:
        logger.error(f"Error creating resilient Deepgram STT: {e}")
        # Wait a moment and try again
        asyncio.sleep(2)
        return ResilientDeepgramSTT(**kwargs)
