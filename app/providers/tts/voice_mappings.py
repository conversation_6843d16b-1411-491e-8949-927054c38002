"""
Voice mappings for different TTS providers.
This file contains mappings between voice IDs and display names for different providers.
"""

# OpenAI voice mappings
OPENAI_VOICE_MAPPING = {
    "alloy": "mira",  # Default voice
    "echo": "<PERSON>",  # Male voice
    "fable": "<PERSON>",  # Female voice
    "onyx": "<PERSON>",  # Male voice
    "nova": "<PERSON>",  # Female voice
}

# OpenAI reverse mapping for user input (lowercase keys for case-insensitive matching)
OPENAI_REVERSE_VOICE_MAPPING = {
    "mira": "alloy",
    "james": "echo",
    "diane": "fable",
    "douglas": "onyx",
    "sandra": "nova",
}

# ElevenLabs voice mappings (placeholder - replace with actual voice IDs)
ELEVENLABS_VOICE_MAPPING = {
    "voice1": "Adam",  # Example voice
    "voice2": "<PERSON>",  # Example voice
    "voice3": "<PERSON>",  # Example voice
    "voice4": "<PERSON>",  # Example voice
    "voice5": "<PERSON>",  # Example voice
}

# ElevenLabs reverse mapping
ELEVENLABS_REVERSE_VOICE_MAPPING = {
    "adam": "voice1",
    "emily": "voice2",
    "michael": "voice3",
    "sophia": "voice4",
    "david": "voice5",
}

# Provider to voice mapping dictionary
PROVIDER_VOICE_MAPPINGS = {
    "openai": {
        "mapping": OPENAI_VOICE_MAPPING,
        "reverse_mapping": OPENAI_REVERSE_VOICE_MAPPING,
        "default_voice": "alloy",
    },
    "elevenlabs": {
        "mapping": ELEVENLABS_VOICE_MAPPING,
        "reverse_mapping": ELEVENLABS_REVERSE_VOICE_MAPPING,
        "default_voice": "voice1",
    },
}


def get_voice_mapping(provider="openai"):
    """
    Get the voice mapping for a specific provider.

    Args:
        provider: The name of the provider

    Returns:
        A tuple of (mapping, reverse_mapping, default_voice)

    Note:
        If the provider is not found, returns the mapping for the default provider (openai)
    """
    # Always use openai as fallback if the requested provider is not available
    if provider != "openai" and provider not in PROVIDER_VOICE_MAPPINGS:
        import logging

        logging.warning(
            f"Voice mapping for provider '{provider}' not found, using 'openai' instead"
        )
        provider = "openai"

    provider_config = PROVIDER_VOICE_MAPPINGS.get(
        provider, PROVIDER_VOICE_MAPPINGS["openai"]
    )
    return (
        provider_config["mapping"],
        provider_config["reverse_mapping"],
        provider_config["default_voice"],
    )


def get_display_name(provider, voice_id):
    """
    Get the display name for a voice ID from a specific provider.

    Args:
        provider: The name of the provider
        voice_id: The voice ID

    Returns:
        The display name for the voice ID, or the voice ID itself if not found
    """
    mapping, _, _ = get_voice_mapping(provider)
    return mapping.get(voice_id, voice_id.capitalize())


def get_voice_id(provider, display_name):
    """
    Get the voice ID for a display name from a specific provider.

    Args:
        provider: The name of the provider
        display_name: The display name (case-insensitive)

    Returns:
        The voice ID for the display name, or None if not found
    """
    _, reverse_mapping, _ = get_voice_mapping(provider)
    return reverse_mapping.get(display_name.lower())


def get_default_voice(provider):
    """
    Get the default voice for a specific provider.

    Args:
        provider: The name of the provider

    Returns:
        The default voice ID for the provider
    """
    _, _, default_voice = get_voice_mapping(provider)
    return default_voice


def get_available_voices(provider):
    """
    Get the available voices for a specific provider.

    Args:
        provider: The name of the provider

    Returns:
        A list of voice IDs for the provider
    """
    mapping, _, _ = get_voice_mapping(provider)
    return list(mapping.keys())


def get_available_display_names(provider):
    """
    Get the available display names for a specific provider.

    Args:
        provider: The name of the provider

    Returns:
        A list of display names for the provider
    """
    mapping, _, _ = get_voice_mapping(provider)
    return list(mapping.values())
