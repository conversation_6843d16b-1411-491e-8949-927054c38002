import logging
from typing import Any, Dict, List, Optional, Tuple

from livekit.plugins import openai

from app.providers.tts.base_provider import BaseTTSProvider
from app.providers.tts.voice_mappings import get_voice_mapping

logger = logging.getLogger(__name__)


class OpenAITTSProvider(BaseTTSProvider):
    """OpenAI TTS provider implementation."""

    def get_tts_instance(self, voice: str, **kwargs) -> Any:
        """
        Get an OpenAI TTS instance.

        Args:
            voice: The OpenAI voice to use (alloy, echo, fable, onyx, nova, shimmer)
            **kwargs: Additional parameters like model, speed

        Returns:
            An OpenAI TTS instance
        """
        model = kwargs.get("model", "tts-1")
        speed = kwargs.get("speed", "1")

        logger.debug(
            f"Creating OpenAI TTS with voice={voice}, model={model}, speed={speed}"
        )
        return openai.TTS(voice=voice, model=model, speed=speed)

    def update_options(self, tts_instance: Any, voice: str, **kwargs) -> None:
        """
        Update options for an existing OpenAI TTS instance.

        Args:
            tts_instance: The OpenAI TTS instance to update
            voice: The voice to use
            **kwargs: Additional parameters like model, speed
        """
        model = kwargs.get("model", "tts-1")
        speed = kwargs.get("speed", "1")

        logger.debug(
            f"Updating OpenAI TTS with voice={voice}, model={model}, speed={speed}"
        )
        tts_instance.update_options(voice=voice, model=model, speed=speed)

    def get_provider_name(self) -> str:
        """
        Get the name of this provider.

        Returns:
            The provider name as a string
        """
        return "openai"

    def get_available_voices(self) -> List[str]:
        """
        Get a list of available voice IDs for this provider.

        Returns:
            A list of voice IDs
        """
        mapping, _, _ = self.get_voice_mapping()
        return list(mapping.keys())

    def get_voice_mapping(self) -> Tuple[Dict[str, str], Dict[str, str], str]:
        """
        Get the voice mapping for this provider.

        Returns:
            A tuple of (mapping, reverse_mapping, default_voice)
        """
        return get_voice_mapping("openai")
