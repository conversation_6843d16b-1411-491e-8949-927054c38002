import logging
from typing import Any, Dict, List, Optional, Tuple

from livekit.agents import tokenize, tts

from app.providers.tts.base_provider import BaseTTSProvider
from app.providers.tts.elevenlabs_provider import ElevenLabsTTSProvider
from app.providers.tts.openai_provider import OpenAITTSProvider
from app.providers.tts.voice_mappings import get_default_voice, get_voice_mapping
from config import DEFAULT_PROVIDER

logger = logging.getLogger(__name__)

# Registry of available TTS providers
TTS_PROVIDERS = {
    "openai": OpenAITTSProvider(),
    # Add more providers here as they become available
    # Note: ElevenLabs provider is commented out because it's not fully implemented yet
    # and will raise NotImplementedError if used
    # "elevenlabs": ElevenLabsTTSProvider(),
}


def get_tts_provider(provider_name: str = DEFAULT_PROVIDER) -> BaseTTSProvider:
    """
    Get a TTS provider by name.

    Args:
        provider_name: The name of the provider to get

    Returns:
        A TTS provider instance

    Note:
        If the provider is not found, returns the default provider (openai)
    """
    # Always use the default provider if the requested provider is not available
    if provider_name != DEFAULT_PROVIDER and provider_name not in TTS_PROVIDERS:
        logger.warning(
            f"TTS provider '{provider_name}' not found, using default '{DEFAULT_PROVIDER}'"
        )
        provider_name = DEFAULT_PROVIDER

    return TTS_PROVIDERS[provider_name]


def get_voice_mappings(
    provider_name: str = DEFAULT_PROVIDER,
) -> Tuple[Dict[str, str], Dict[str, str], str]:
    """
    Get the voice mappings for a provider.

    Args:
        provider_name: The name of the provider to get mappings for

    Returns:
        A tuple of (mapping, reverse_mapping, default_voice)
    """
    provider = get_tts_provider(provider_name)
    return provider.get_voice_mapping()


def get_available_voices(provider_name: str = DEFAULT_PROVIDER) -> List[str]:
    """
    Get the available voices for a provider.

    Args:
        provider_name: The name of the provider to get voices for

    Returns:
        A list of voice IDs
    """
    provider = get_tts_provider(provider_name)
    return provider.get_available_voices()


def create_tts_instance(
    provider_name: str = DEFAULT_PROVIDER,
    voice: str = None,
    tokenizer: Optional[Any] = None,
    **kwargs,
) -> tts.StreamAdapter:
    """
    Create a TTS instance with the specified provider and voice.

    Args:
        provider_name: The name of the TTS provider to use
        voice: The voice identifier to use
        tokenizer: Optional tokenizer to use with the StreamAdapter
        **kwargs: Additional provider-specific parameters

    Returns:
        A StreamAdapter instance ready to use with VoiceAssistant
    """
    provider = get_tts_provider(provider_name)

    # If no voice is specified, use the default voice for the provider
    if voice is None:
        _, _, default_voice = provider.get_voice_mapping()
        voice = default_voice
        logger.debug(f"Using default voice '{voice}' for provider '{provider_name}'")

    # Create the TTS instance
    base_tts = provider.get_tts_instance(voice, **kwargs)

    # Use the provided tokenizer or create a default one
    sentence_tokenizer = tokenizer or tokenize.basic.SentenceTokenizer()

    # Create and return the StreamAdapter
    return tts.StreamAdapter(
        tts=base_tts,
        sentence_tokenizer=sentence_tokenizer,
    )
