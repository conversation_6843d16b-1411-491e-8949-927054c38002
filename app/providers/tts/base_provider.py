from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Tuple


class BaseTTSProvider(ABC):
    """Base class for all TTS providers."""

    @abstractmethod
    def get_tts_instance(self, voice: str, **kwargs) -> Any:
        """
        Get a TTS instance from this provider.

        Args:
            voice: The voice identifier to use
            **kwargs: Additional provider-specific parameters

        Returns:
            A TTS instance that can be used with StreamAdapter
        """
        pass

    @abstractmethod
    def update_options(self, tts_instance: Any, voice: str, **kwargs) -> None:
        """
        Update options for an existing TTS instance.

        Args:
            tts_instance: The TTS instance to update
            voice: The voice identifier to use
            **kwargs: Additional provider-specific parameters
        """
        pass

    @abstractmethod
    def get_provider_name(self) -> str:
        """
        Get the name of this provider.

        Returns:
            The provider name as a string
        """
        pass

    @abstractmethod
    def get_available_voices(self) -> List[str]:
        """
        Get a list of available voice IDs for this provider.

        Returns:
            A list of voice IDs
        """
        pass

    @abstractmethod
    def get_voice_mapping(self) -> Tuple[Dict[str, str], Dict[str, str], str]:
        """
        Get the voice mapping for this provider.

        Returns:
            A tuple of (mapping, reverse_mapping, default_voice)
        """
        pass
