# app/state/user_state.py
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional  # Added List, Any

# import bson # Not needed if not directly handling bson.ObjectId here
# from bson import ObjectId # Not needed if not directly handling bson.ObjectId here

# Assuming config.py defines these or they are passed/set differently
# For now, defining them here if not available elsewhere in this context
DEFAULT_PROVIDER = "openai"
DEFAULT_QUIT_MODE = False  # Example
DEFAULT_QUIT_MODE_END = "22:00"  # Example
DEFAULT_VOICE = "alloy"  # Example, from your env_constants
DEFAULT_DAILY_UPDATES_TIME = "08:00"  # Example


class UserState:
    """Track the state of the user onboarding process and user preferences"""

    def __init__(self, user_id: Optional[str] = None):
        # Onboarding state tracking
        self.onboarding_step = "greeting"
        self.current_voice_index = 0

        self.user_id = user_id
        # self.id = None # Removed, use user_id directly or via property if needed
        self.participant_id = None

        self.name: Optional[str] = None
        self.preferred_name: Optional[str] = None
        self.phone_number: Optional[str] = None
        self.city: Optional[str] = None
        self.state: Optional[str] = None
        self.zip_code: Optional[str] = None
        self.birth_day: Optional[str] = None
        self.birth_month: Optional[str] = None

        self.voice_name: str = DEFAULT_VOICE
        self.tts_provider: str = DEFAULT_PROVIDER
        self.tts_settings: dict = {}

        self.daily_updates_time: str = DEFAULT_DAILY_UPDATES_TIME
        self.quite_mode: bool = DEFAULT_QUIT_MODE  # Corrected typo from 'quit_mode'
        self.quite_mode_end_time: str = DEFAULT_QUIT_MODE_END

        self.onboarding_stage_completed: List[str] = []
        # self.contact_id: Optional[str] = None # This seems more like part of ContactState or a specific relation
        self.is_synced: bool = False

        self.is_initial_setup_complete: bool = False
        self.setup_reminder_timestamp: Optional[datetime] = (
            None  # Should be datetime if storing timestamp
        )
        self.last_interaction_timestamp: datetime = datetime.now(timezone.utc)
        self.time_zone = "UTC"  # Default timezone - using UTC as the standard default
        # self.last_interacted_at: datetime = datetime.now(timezone.utc) # Redundant with last_interaction_timestamp

        self.current_flow: str = None
        self.current_flow_id: Optional[str] = None

    def load_from_dict(self, data: Dict[str, Any]):
        """Populates UserState attributes from a dictionary (e.g., API response)."""
        if not data:
            return

        self.user_id = (
            data.get("user_id", self.user_id)
            or data.get("_id", self.user_id)
            or data.get("id", self.user_id)
        )
        self.participant_id = data.get("participant_id", self.participant_id)

        self.name = data.get("name", self.name)
        self.preferred_name = data.get("preferred_name", self.preferred_name)
        self.phone_number = data.get("phone_number", self.phone_number)
        self.city = data.get("city", self.city)
        self.state = data.get("state", self.state)
        self.zip_code = data.get("zip_code", self.zip_code)
        self.birth_day = data.get("birth_day", self.birth_day)
        self.birth_month = data.get("birth_month", self.birth_month)

        self.voice_name = data.get("voice_name", self.voice_name) or DEFAULT_VOICE
        self.tts_provider = (
            data.get("tts_provider", self.tts_provider) or DEFAULT_PROVIDER
        )
        # tts_settings could be more complex, assuming it's a dict in the data
        self.tts_settings = data.get("tts_settings", self.tts_settings) or {}

        self.daily_updates_time = (
            data.get("daily_updates_time", self.daily_updates_time) or ""
        )
        self.quite_mode = data.get(
            "quite_mode", self.quite_mode
        )  # Typo fixed to 'quite_mode'
        if self.quite_mode is None:
            self.quite_mode = DEFAULT_QUIT_MODE  # ensure bool

        self.quite_mode_end_time = (
            data.get("quite_mode_end_time", self.quite_mode_end_time)
            or DEFAULT_QUIT_MODE_END
        )

        # Ensure onboarding_stage_completed is always a list
        onboarding_stages = data.get(
            "onboarding_stage_completed", self.onboarding_stage_completed
        )
        self.onboarding_stage_completed = (
            list(onboarding_stages)
            if isinstance(onboarding_stages, (list, tuple, set))
            else []
        )

        self.is_synced = data.get("is_synced", self.is_synced)
        if self.is_synced is None:
            self.is_synced = False  # ensure bool

        self.is_initial_setup_complete = data.get(
            "is_initial_setup_complete", self.is_initial_setup_complete
        )
        if self.is_initial_setup_complete is None:
            self.is_initial_setup_complete = False  # ensure bool

        # Handle last_interacted_at (prefer it over last_interaction_timestamp if both exist)
        last_interacted_str = data.get("last_interacted_at") or data.get(
            "last_interaction_timestamp"
        )
        if last_interacted_str:
            try:
                # Attempt to parse if it's a string (e.g., ISO format from DB)
                if isinstance(last_interacted_str, str):
                    self.last_interaction_timestamp = datetime.fromisoformat(
                        last_interacted_str.replace("Z", "+00:00")
                    )
                elif isinstance(
                    last_interacted_str, datetime
                ):  # If it's already a datetime object
                    self.last_interaction_timestamp = last_interacted_str
            except ValueError:
                print(f"Could not parse last_interacted_at: {last_interacted_str}")
                self.last_interaction_timestamp = datetime.now(timezone.utc)  # Fallback
        else:
            self.last_interaction_timestamp = datetime.now(
                timezone.utc
            )  # Fallback if not in data

        # Handle timezone
        self.time_zone = data.get("time_zone", self.time_zone)
        if not self.time_zone:
            self.time_zone = "UTC"  # Ensure we always have a default timezone

        # If is_initial_setup_complete is true, ensure onboarding_stage_completed reflects this
        if self.is_initial_setup_complete:
            required_stages = {"name", "voice", "contact"}
            current_stages = set(self.onboarding_stage_completed)
            for stage in required_stages:
                if stage not in current_stages:
                    self.onboarding_stage_completed.append(stage)

        print(
            f"UserState loaded from dict. User ID: {self.user_id}, Name: {self.name}, Setup Complete: {self.is_initial_setup_complete}"
        )

    # Removed redundant properties like user_name, id, selected_voice, onboarding_complete
    # as they are now directly handled or their logic is incorporated into load_from_dict or __init__.
    # The tts_config property can remain if useful.

    @property
    def tts_config(self):
        """Get the TTS configuration as a dictionary"""
        return {
            "provider": self.tts_provider,
            "voice": self.voice_name,
            **self.tts_settings,
        }

    def to_dict(self):
        """Convert user state to dictionary for database storage"""
        # Ensure all fields are consistent with what load_from_dict expects
        # and what your database schema requires.
        return {
            "user_id": self.user_id,  # Often corresponds to _id in MongoDB if you set it explicitly
            "participant_id": self.participant_id,
            "name": self.name,
            "preferred_name": self.preferred_name,
            "phone_number": self.phone_number,
            "city": self.city,
            "state": self.state,
            "zip_code": self.zip_code,
            "birth_day": self.birth_day,
            "birth_month": self.birth_month,
            "voice_name": self.voice_name,
            "tts_provider": self.tts_provider,
            "tts_settings": self.tts_settings,
            "daily_updates_time": self.daily_updates_time,
            "quite_mode": self.quite_mode,  # Typo fixed
            "quite_mode_end_time": self.quite_mode_end_time,
            "onboarding_stage_completed": self.onboarding_stage_completed,
            "is_synced": self.is_synced,
            "is_initial_setup_complete": self.is_initial_setup_complete,
            "time_zone": self.time_zone,  # Include timezone in the dictionary
            "setup_reminder_timestamp": (
                self.setup_reminder_timestamp.isoformat()
                if self.setup_reminder_timestamp
                else None
            ),
            # Use this consistent field
            "last_interacted_at": self.last_interaction_timestamp.isoformat(),
            # "created_at" and "updated_at" are usually handled by the database/ORM
        }
