# app/state/reminder_state.py
from datetime import datetime, timedelta
from typing import Optional

from app.constants.env_constants import (
    DEFAULT_APPOINTMENT_NOTIFY_BEFORE,
)  # Make sure to import Optional


class ReminderState:
    def __init__(self, user_id: Optional[str] = None):  # Added user_id parameter
        self.user_id = user_id  # Store user_id
        self.id = None  # Unique identifier for the reminder
        self.reminder_text = None
        self.reminder_type = "general"
        self.trigger_time = None  # datetime object
        self.is_recurring = False
        self.recurrence_rule = None
        self.notify_before = DEFAULT_APPOINTMENT_NOTIFY_BEFORE
        self.linked_entity = None
        self.alert_method = "voice"
        self.is_active = True

    def compute_trigger_time(self, base_time=None):
        """
        Optionally compute trigger_time as base_time - notify_before if not set.
        """
        if not self.trigger_time and base_time:
            self.trigger_time = base_time - timedelta(minutes=self.notify_before)

    def to_dict(self):
        """Convert reminder state to dictionary for database storage"""
        data = {
            "id": self.id,
            "reminder_text": self.reminder_text,
            "reminder_type": self.reminder_type,
            "trigger_time": (
                self.trigger_time.isoformat() if self.trigger_time else None
            ),
            "is_recurring": self.is_recurring,
            "recurrence_rule": self.recurrence_rule,
            "notify_before": self.notify_before,
            "linked_entity": self.linked_entity,
            "alert_method": self.alert_method,
            "is_active": self.is_active,
        }
        if self.user_id:  # Include user_id if it's set
            data["user_id"] = self.user_id
        return data
