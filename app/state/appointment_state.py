from dataclasses import dataclass, field
from datetime import datetime
from typing import List, Optional


@dataclass
class AppointmentState:
    user_id: Optional[str] = None
    title: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    location: Optional[str] = None
    description: Optional[str] = None
    is_recurring: bool = False
    recurrence_rule: Optional[str] = None
    asked_for_person_name: bool = False
    participants: List[str] = field(default_factory=list)
    step: str = "title"
    confirmed: bool = False

    def to_dict(self) -> dict:
        return {
            "user_id": self.user_id,
            "title": self.title,
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "end_time": self.end_time.isoformat() if self.end_time else None,
            "location": self.location,
            "description": self.description,
            "is_recurring": self.is_recurring,
            "recurrence_rule": self.recurrence_rule,
            "participants": self.participants,
        }

    def reset(self):
        self.user_id = None
        self.title = None
        self.start_time = None
        self.end_time = None
        self.location = None
        self.description = None
        self.is_recurring = False
        self.recurrence_rule = None
        self.participants = []
        self.step = "title"
        self.confirmed = False
