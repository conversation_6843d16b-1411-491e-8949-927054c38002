import logging
from typing import Any, Dict, List, Optional

from app.constants.action_types import RelationType
from app.state.user_state import UserState

logger = logging.getLogger(__name__)

CONTACT_ONBOARDING_DETAIL_SEQUENCE: Dict[str, List[str]] = {
    RelationType.parent.value: ["nick_name", "interest", "is_emergency_contact"],
    RelationType.spouse.value: ["nick_name", "interest", "is_emergency_contact"],
    RelationType.child.value: ["nick_name", "school", "interest"],
    RelationType.sibling.value: ["nick_name", "interest", "is_emergency_contact"],
    RelationType.grandchild.value: ["nick_name", "school", "interest"],
    RelationType.greatgrandchild.value: ["nick_name", "school", "interest"],
    RelationType.friend.value: ["nick_name", "interest", "job", "best_way_to_reach"],
    RelationType.neighbor.value: ["nick_name", "interest", "best_way_to_reach"],
    RelationType.doctor.value: [
        "job",
        "phone_number",
        "is_emergency_contact",
        "best_way_to_reach",
    ],
    RelationType.caregiver.value: [
        "phone_number",
        "is_emergency_contact",
        "best_way_to_reach",
    ],
}

# Make a list of all relations using RelationType enum
CONTACT_UPDATE_RELATIONS = [relation.value for relation in RelationType]


class ContactState:
    def __init__(self, user_id: str):
        self.user_id: str = user_id
        self.onboarding_step: str = ""  # Tracks onboarding progress
        self.current_relation_idx: int = -1
        self.current_onboarding_relation: Optional[str] = None
        self.relations_to_onboard: List[str] = []
        self.filtered_relations: List[str] = []
        self.current_contact_id: Optional[str] = None
        self.current_contact_name: Optional[str] = None
        self.current_contact_first_name: Optional[str] = None
        self.current_contact_last_name: Optional[str] = None
        self.temp_details_for_current_contact: Dict[str, Any] = {}
        self.last_search_results: List[Dict[str, Any]] = []
        self.is_myhoudini_contact: bool = False
        self.filtered_contact_relations_for_prompt: str = ""

    def validate_state(self) -> bool:
        """Ensures current_relation_idx is valid and resets if necessary."""
        if (
            self.current_relation_idx >= len(self.relations_to_onboard)
            and self.current_relation_idx != -1
        ):
            logger.warning(f"Invalid current_relation_idx: {self.current_relation_idx}")
            self.current_relation_idx = -1
            self.current_onboarding_relation = None
            self.onboarding_step = "contact_onboarding_complete"
            return False
        return True

    def reset_for_next_relation(self) -> None:
        """Clears temporary state for the next relation."""
        self.current_contact_id = None
        self.current_contact_name = None
        self.current_contact_first_name = None
        self.current_contact_last_name = None
        self.temp_details_for_current_contact = {}
        self.last_search_results = []
        self.onboarding_step = "ask_relation_name"

    def start_onboarding_relations(
        self,
        all_synced_contacts_from_db: List[Dict[str, Any]],
        relations_to_process: List[str],
    ) -> None:
        """Initializes onboarding with synced contacts and relations."""
        self.relations_to_onboard = relations_to_process
        self.current_relation_idx = -1
        self.current_onboarding_relation = None
        self.onboarding_step = "ask_relation_name"
        self.filtered_relations = relations_to_process
        self.is_myhoudini_contact = True
        logger.info(f"Started onboarding for relations: {self.relations_to_onboard}")

    def move_to_next_relation_for_onboarding(self) -> bool:
        """Moves to the next relation in the onboarding list."""
        self.current_relation_idx += 1
        if self.current_relation_idx < len(self.relations_to_onboard):
            self.current_onboarding_relation = self.relations_to_onboard[
                self.current_relation_idx
            ]
            self.reset_for_next_relation()
            self.onboarding_step = "ask_relation_name"
            logger.info(
                f"Moved to next relation for onboarding: {self.current_onboarding_relation} (Index {self.current_relation_idx})"
            )
            return True
        else:
            self.current_onboarding_relation = None
            self.onboarding_step = "contact_onboarding_complete"
            self.current_relation_idx = -1
            logger.info("All filtered contact onboarding relations processed.")
            return False

    def set_current_contact_found(self, found_contact: Dict[str, Any]) -> None:
        """Sets state for a single found contact."""
        self.current_contact_id = str(found_contact.get("_id"))
        self.current_contact_name = found_contact.get("name")
        self.current_contact_first_name = found_contact.get("first_name")
        self.current_contact_last_name = found_contact.get("last_name")
        self.temp_details_for_current_contact = {
            "nick_name": found_contact.get("nick_name"),
            "interest": found_contact.get("interest"),
            "school": found_contact.get("school"),
            "job": found_contact.get("job"),
            "best_way_to_reach": found_contact.get("best_way_to_reach"),
            "is_emergency_contact": found_contact.get("is_emergency_contact"),
            "phone_number": (
                found_contact.get("phone_numbers", [{}])[0].get("number")
                if found_contact.get("phone_numbers")
                else None
            ),
            "email": (
                found_contact.get("emails", [{}])[0].get("address")
                if found_contact.get("emails")
                else None
            ),
        }
        self.temp_details_for_current_contact = {
            k: v
            for k, v in self.temp_details_for_current_contact.items()
            if v is not None
        }
        self.onboarding_step = "ask_remaining_details"
        logger.info(
            f"Set current contact: ID {self.current_contact_id}, Name {self.current_contact_name}"
        )

    def set_contact_search_ambiguous(self, contacts: List[Dict[str, Any]]) -> None:
        """Sets state for multiple found contacts."""
        self.last_search_results = contacts
        self.onboarding_step = "confirm_multiple_contacts"
        logger.info(f"Set ambiguous search results: {len(contacts)} contacts")

    def complete_onboarding_stage(self, user_state: UserState) -> None:
        """Marks contact onboarding as complete."""
        user_state.onboarding_stage_completed.append("contact")
        user_state.is_initial_setup_complete = True
        self.onboarding_step = "contact_onboarding_complete"
        self.current_relation_idx = -1
        self.current_onboarding_relation = None
        logger.info("Contact onboarding stage completed.")

    def get_current_relation_details_to_ask(self) -> List[str]:
        """Returns the details to ask for the current relation."""
        if not self.current_onboarding_relation:
            logger.warning("No current onboarding relation set.")
            return []
        details = CONTACT_ONBOARDING_DETAIL_SEQUENCE.get(
            self.current_onboarding_relation, []
        )
        remaining_details = [
            detail
            for detail in details
            if detail not in self.temp_details_for_current_contact
        ]
        logger.info(
            f"Details to ask for {self.current_onboarding_relation}: {remaining_details}"
        )
        return remaining_details
