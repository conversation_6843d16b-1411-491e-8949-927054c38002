# app/session_data_defs.py
from typing import Any, Dict, List, Optional

from livekit import rtc
from livekit.agents import RunContext

from app.state.appointment_state import AppointmentState
from app.state.contact_state import (
    ContactState,
)
from app.state.reminder_state import ReminderState

# Import the state classes that SessionData will hold
from app.state.user_state import UserState


# Define UserDataStructure here
class SessionData:
    def __init__(
        self,
        room: rtc.Room,
        user_id: Optional[str] = None,
    ):
        self.room = room
        self.user_state = UserState(user_id=user_id)
        self.contact_state = ContactState(user_id=user_id)
        self.appointment_state = AppointmentState(user_id=user_id if user_id else None)
        self.reminder_state = ReminderState(user_id=user_id if user_id else None)

        self.current_contact_search_results: List[Dict[str, Any]] = []
        self.selected_contact_for_update: Optional[Dict[str, Any]] = (
            None  # For multi-step contact updates
        )
        # For storing results of find_reminder/find_appointment temporarily for indexed selection
        self.current_reminder_search_results: List[Dict[str, Any]] = []
        self.current_appointment_search_results: List[Dict[str, Any]] = []

        self.room_name = room.name

        # For the improved appointment update flow
        self.selected_appointment_to_update: Optional[Dict[str, Any]] = None
        self.last_appointment_date_query: Optional[str] = None


# # Type hint for context in tools
UserDataContext = RunContext[SessionData]

START_ONBOARDING_ASSISTANT_DIALOG = """
Hi there — I’m MyHoudini, your personal voice assistant.

I’m here to help you stay organized, remember important things, and keep in touch with the people who matter most.

You don’t need to tap or type anything — just talk to me, and I’ll take care of the rest.

To get started, I’ll ask a few simple questions about you — like who you’d like me to remember, what kinds of things you do each day, and when you’d like reminders.

From now on, I’ll help you: plan your day, answer questions, share the news, and maybe even play a game or two.

Let's get started with some basic questions about you.

First, what's your full name?
"""
