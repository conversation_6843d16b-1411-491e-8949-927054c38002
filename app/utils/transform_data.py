from typing import Annotated, Any, Dict, List, Optional, Union


def transform_contact_data(incoming_data):
    transformed_data = incoming_data.copy()

    # Handle phone numbers (both single string and list formats)
    if "phone_number" in transformed_data:
        transformed_data["phone_numbers"] = [
            {"number": transformed_data["phone_number"], "label": "mobile"}
        ]
        del transformed_data["phone_number"]  # Remove the single 'phone_number' field
    elif "phone_numbers" in transformed_data:
        if isinstance(transformed_data["phone_numbers"], str):
            transformed_data["phone_numbers"] = [
                {"number": transformed_data["phone_numbers"], "label": "mobile"}
            ]
        elif isinstance(transformed_data["phone_numbers"], list):
            for item in transformed_data["phone_numbers"]:
                if "label" not in item:
                    item["label"] = "mobile"

    # Handle email addresses (single string and list formats)
    if "email" in transformed_data and isinstance(transformed_data["email"], str):
        transformed_data["email"] = [
            {"email": transformed_data["email"], "label": "home"}
        ]
    elif "email" in transformed_data and isinstance(transformed_data["email"], list):
        for item in transformed_data["email"]:
            if "label" not in item:
                item["label"] = "home"

    # Handle addresses (single string and list formats)
    if "address" in transformed_data and isinstance(transformed_data["address"], str):
        # Assuming a simple string needs more context to split into parts
        # This is a simplified example
        transformed_data["address"] = [
            {"street": transformed_data["address"], "label": "home"}
        ]
    elif "address" in transformed_data and isinstance(
        transformed_data["address"], list
    ):
        for item in transformed_data["address"]:
            if "label" not in item:
                item["label"] = "home"

    return transformed_data


def extract_contact_details(contact: Dict[str, Any]) -> Dict[str, Any]:
    """
    Extracts relevant contact details from a contact dictionary for inclusion in event data.

    Args:
        contact: Dictionary containing contact data from search_contacts_by_name.

    Returns:
        Dictionary with fields: name, phone_number, email, address, relationship, hobbies.
    """
    details = {}
    contact_details = contact.get("details", {})

    # Extract name (top-level or details)
    if contact.get("name"):
        details["name"] = contact["name"]
    elif contact_details.get("name"):
        details["name"] = contact_details["name"]

    # Extract phone_number (string from details)
    phone_number = contact_details.get("phone_number")
    if isinstance(phone_number, str) and phone_number:
        details["phone_number"] = phone_number
    # Fallback: check top-level phone_numbers as list
    elif (
        isinstance(contact.get("phone_numbers"), list)
        and contact["phone_numbers"]
        and isinstance(contact["phone_numbers"][0], dict)
    ):
        details["phone_number"] = contact["phone_numbers"][0].get("number", "")

    # Extract email (string or list of dicts from details)
    email = contact_details.get("email")
    if isinstance(email, str) and email:
        details["email"] = email
    elif (
        isinstance(email, list)
        and email
        and isinstance(email[0], dict)
        and email[0].get("email")
    ):
        details["email"] = email[0]["email"]
    # Fallback: check top-level email
    elif isinstance(contact.get("email"), str) and contact["email"]:
        details["email"] = contact["email"]

    # Extract address (string or list of dicts from details)
    address = contact_details.get("address")
    if isinstance(address, str) and address:
        details["address"] = address
    elif isinstance(address, list) and address and isinstance(address[0], dict):
        addr_dict = address[0]
        addr_parts = []
        if addr_dict.get("street"):
            addr_parts.append(addr_dict["street"])
        for key in ["city", "state", "postCode", "country"]:
            if addr_dict.get(key):
                addr_parts.append(str(addr_dict[key]))
        if addr_dict.get("label"):
            addr_parts.append(addr_dict["label"])
        details["address"] = ", ".join(addr_parts) if addr_parts else ""
    # Fallback: check top-level address
    elif isinstance(contact.get("address"), str) and contact["address"]:
        details["address"] = contact["address"]

    # Extract relationship (details or top-level)
    if contact_details.get("relationship"):
        details["relationship"] = contact_details["relationship"]
    elif contact.get("relationship"):
        details["relationship"] = contact["relationship"]

    # Extract hobbies (details or top-level)
    if contact_details.get("hobbies"):
        details["hobbies"] = contact_details["hobbies"]
    elif contact.get("hobbies"):
        details["hobbies"] = contact["hobbies"]

    return details
