import logging
import re
from datetime import datetime, timedelta, timezone
from typing import Optional, Tuple, Union

from dateutil.parser import parse as dateutil_parse  # For parsing date strings
from dateutil.rrule import rrulestr as dateutil_rrulestr

from app.utils.reminder_time import parse_reminder

# Try to import ZoneInfo (Python 3.9+)
try:
    from zoneinfo import ZoneInfo, ZoneInfoNotFoundError
except ImportError:
    # Fallback for Python < 3.9
    ZoneInfo = None
    ZoneInfoNotFoundError = Exception

logger = logging.getLogger(__name__)


def get_user_timezone(user_state):
    """
    Gets the user's timezone object, defaulting to UTC.

    Args:
        user_state: The user state object containing timezone information

    Returns:
        A timezone object (from ZoneInfo, pytz, or timezone.utc)
    """
    # Get the timezone string from user_state
    user_timezone_str = getattr(user_state, "time_zone")
    logger.info(f"User timezone: {user_timezone_str}")

    try:
        # Try to use ZoneInfo first (Python 3.9+)
        try:
            if ZoneInfo:
                tz = ZoneInfo(user_timezone_str)
                return tz
        except (ZoneInfoNotFoundError, ImportError):
            # Fall back to pytz if ZoneInfo fails
            try:
                import pytz

                return pytz.timezone(user_timezone_str)
            except (ImportError, Exception):
                # If all else fails, use UTC
                logger.warning(
                    f"Could not load timezone '{user_timezone_str}'. Using UTC."
                )
                return timezone.utc
    except Exception as tz_err:
        logger.warning(
            f"Error loading timezone '{user_timezone_str}': {tz_err}. Defaulting to UTC."
        )
        return timezone.utc


def _prepare_api_date(date_query_str: Optional[str], now_dt: datetime) -> Optional[str]:
    """Converts natural language date queries to YYYY-MM-DD for the API."""
    if not date_query_str:
        return None

    # Try with parse_reminder first for "today", "tomorrow", etc.
    try:
        parsed_info = parse_reminder(date_query_str, current_time=now_dt)
        if parsed_info and parsed_info.get("trigger_time"):
            return parsed_info["trigger_time"].strftime("%Y-%m-%d")
    except (
        Exception
    ):  # Fallback if parse_reminder fails or doesn't return expected structure
        pass

    # Fallback to dateutil_parse for specific dates
    try:
        return dateutil_parse(date_query_str).strftime("%Y-%m-%d")
    except (ValueError, TypeError):
        print(f"Could not parse date_query '{date_query_str}' to YYYY-MM-DD format.")
        return None  # Or raise ToolError if strict parsing is required by API


def _prepare_api_time(time_query_str: Optional[str]) -> Optional[str]:
    """Converts natural language time queries to HH:MM for the API."""
    if not time_query_str:
        return None

    # This is a simplified parser. parse_reminder or a more robust time parser would be better.
    # Example: "8 pm" -> "20:00", "9am" -> "09:00", "morning" -> could be a range (API might not support)
    # For now, let's assume the LLM or user provides something close to HH:MM or H:M AM/PM
    try:
        # Attempt to parse various common time formats
        # Handle "8pm", "9 AM", "14:30"
        cleaned_time = time_query_str.lower().replace(" ", "")

        # Try HH:MM first
        if re.match(r"^\d{1,2}:\d{2}$", cleaned_time):
            parsed_dt = datetime.strptime(cleaned_time, "%H:%M")
            return parsed_dt.strftime("%H:%M")

        # Try H AM/PM or HH AM/PM or H:MM AM/PM etc.
        # dateutil.parser is quite good at this if only time is given
        parsed_dt = dateutil_parse(time_query_str)
        return parsed_dt.strftime("%H:%M")

    except (ValueError, TypeError):
        # Handle "morning", "afternoon", "evening" - API might not support these directly.
        # If API expects specific HH:MM, these qualitative times are harder.
        # The LLM might need to translate them or the API needs to understand ranges.
        # For now, we'll pass them as is if the API can handle or ignore them, or return None.
        if time_query_str.lower() in ["morning", "afternoon", "evening", "night"]:
            print(
                f"Qualitative time query '{time_query_str}' may not be directly supported by API's HH:MM filter."
            )
            return None  # Or return a representative time like "09:00" for morning if API needs something

        print(f"Could not parse time_query '{time_query_str}' to HH:MM format.")
        return None


async def get_valid_datetime(
    self,
    prompt: str,
    allow_skip: bool = False,
    reference_time: Optional[datetime] = None,
) -> Optional[datetime]:
    """
    Asks the user for a valid datetime input with retries until valid or skipped.
    Args:
        prompt: The prompt to ask the user for input.
        allow_skip: Whether the user can skip the input (returns None).
        reference_time: Optional reference time for validation (e.g., start_time for end_time).
    Returns:
        A validated datetime object or None if skipped.
    """
    while True:
        await self.assistant.say(prompt, allow_interruptions=True)
        # Placeholder for getting user input; assumes a method exists
        user_input = await self.get_user_input()
        if allow_skip and user_input.lower() == "skip":
            return None
        try:
            now = datetime.now(timezone.utc)
            reminder_data = parse_reminder(user_input, current_time=now)
            if not reminder_data or "trigger_time" not in reminder_data:
                raise ValueError("Invalid time format")
            parsed_time = datetime.fromisoformat(
                reminder_data["trigger_time"].replace("Z", "+00:00")
            )
            if reference_time and parsed_time <= reference_time:
                raise ValueError(
                    f"Time must be after {reference_time.strftime('%I:%M %p on %B %d, %Y')}"
                )
            return parsed_time
        except Exception as e:
            error_message = (
                f"I couldn't understand the time '{user_input}'. Please use a format like:\n"
                "- '4pm today'\n- 'tomorrow at 9am'\n- 'April 30th at 3pm'"
            )
            if str(e).startswith("Time must be after"):
                error_message = str(e)
            await self.assistant.say(error_message, allow_interruptions=True)


def convert_local_to_utc(
    time_desc: str,
    user_timezone,
    is_recurring: Optional[bool] = None,
    recurrence_rule: Optional[str] = None,
    allow_past: bool = False,
) -> Tuple[datetime, bool, Optional[str]]:
    """
    Converts a natural language time description to a UTC datetime.

    Args:
        time_desc: Natural language time description (e.g., "tomorrow at 3pm")
        user_timezone: The user's timezone (can be a timezone object or a string like 'Asia/Kolkata')
        is_recurring: Optional flag to indicate if the time is recurring
        recurrence_rule: Optional recurrence rule string
        allow_past: If True, allow past times (for search); if False, raise for past times (for create/update)

    Returns:
        Tuple containing:
        - UTC datetime
        - Boolean indicating if the time is recurring
        - Recurrence rule string (or None if not recurring)

    Raises:
        ValueError: If the time description cannot be parsed or is in the past (when allow_past is False)
    """
    print(
        f"convert_local_to_utc: time_desc={time_desc}, user_timezone={user_timezone}, allow_past={allow_past}"
    )

    # Handle string timezone
    if isinstance(user_timezone, str):
        try:
            # Try to use ZoneInfo first (Python 3.9+)
            try:
                from zoneinfo import ZoneInfo

                tz_obj = ZoneInfo(user_timezone)
            except (ImportError, Exception):
                # Fall back to pytz if ZoneInfo fails
                try:
                    import pytz

                    tz_obj = pytz.timezone(user_timezone)
                except (ImportError, Exception):
                    print(f"Could not load timezone '{user_timezone}'. Using UTC.")
                    tz_obj = timezone.utc
        except Exception as e:
            print(f"Error loading timezone '{user_timezone}': {e}. Defaulting to UTC.")
            tz_obj = timezone.utc
    else:
        tz_obj = user_timezone

    now_local = datetime.now(tz_obj)
    now_utc = now_local.astimezone(timezone.utc)

    # Parse the time description
    parsed_data = parse_reminder(time_desc, current_time=now_local)
    parsed_dt_local = parsed_data.get("trigger_time")

    if not parsed_dt_local or not isinstance(parsed_dt_local, datetime):
        raise ValueError(f"Cannot understand time '{time_desc}'. Try 'tomorrow 2 PM'.")

    # Ensure the datetime is timezone-aware
    if parsed_dt_local.tzinfo is None:
        parsed_dt_local = parsed_dt_local.replace(tzinfo=tz_obj)

    # Convert to UTC
    time_utc = parsed_dt_local.astimezone(timezone.utc)

    # Determine if the time is recurring
    final_is_recurring = (
        is_recurring
        if is_recurring is not None
        else parsed_data.get("is_recurring", False)
    )

    # Get the recurrence rule
    final_recurrence_rule = (
        recurrence_rule
        if recurrence_rule is not None
        else parsed_data.get("recurrence_rule")
    )

    # If a recurrence rule is provided but is_recurring is False, set is_recurring to True
    if final_recurrence_rule and not final_is_recurring:
        final_is_recurring = True

    # Check if the time is in the past
    if not allow_past and time_utc <= now_utc:
        if final_is_recurring:
            # Adjust recurring past time to the next occurrence
            if not final_recurrence_rule:
                adjusted_dt_naive = time_utc.replace(tzinfo=None)
                while adjusted_dt_naive <= now_utc.replace(tzinfo=None):
                    adjusted_dt_naive += timedelta(days=1)
                time_utc = adjusted_dt_naive.replace(tzinfo=timezone.utc)
            else:
                try:
                    rrule_obj = dateutil_rrulestr(
                        final_recurrence_rule,
                        dtstart=time_utc.replace(tzinfo=None),
                    )
                    next_occurrence_naive = rrule_obj.after(
                        now_utc.replace(tzinfo=None)
                    )
                    if next_occurrence_naive:
                        time_utc = next_occurrence_naive.replace(tzinfo=timezone.utc)
                    else:
                        raise ValueError("Cannot calculate next recurring occurrence.")
                except Exception as e:
                    raise ValueError(f"Issue with recurrence rule: {e}")
        else:
            raise ValueError(
                f"Time '{time_desc}' is in the past. Please provide a future time."
            )

    return time_utc, final_is_recurring, final_recurrence_rule


def convert_utc_to_local(dt_utc: datetime, user_timezone) -> datetime:
    """
    Converts a UTC datetime to the user's local timezone.

    Args:
        dt_utc: The UTC datetime (must be timezone-aware, UTC).
        user_timezone: The user's timezone (ZoneInfo, pytz, or string).

    Returns:
        The datetime converted to the user's timezone.
    """
    if not dt_utc or not isinstance(dt_utc, datetime):
        raise ValueError("dt_utc must be a datetime object")
    if dt_utc.tzinfo is None:
        dt_utc = dt_utc.replace(tzinfo=timezone.utc)
    # Handle string timezone
    if isinstance(user_timezone, str):
        try:
            from zoneinfo import ZoneInfo

            tz_obj = ZoneInfo(user_timezone)
        except Exception:
            try:
                import pytz

                tz_obj = pytz.timezone(user_timezone)
            except Exception:
                tz_obj = timezone.utc
    else:
        tz_obj = user_timezone
    return dt_utc.astimezone(tz_obj)


def user_time_to_utc_iso(user_time, user_state, allow_past=False):
    """
    Convert a user-given time (string or datetime) in user's timezone to UTC ISO string for API.
    Set allow_past=True to allow past times (for search).
    """
    user_timezone = get_user_timezone(user_state)
    if isinstance(user_time, datetime):
        local_dt = user_time
    else:
        # Assume string input, use convert_local_to_utc
        local_dt, _, _ = convert_local_to_utc(
            user_time, user_timezone, allow_past=allow_past
        )
    utc_dt = local_dt.astimezone(timezone.utc)
    return utc_dt.isoformat()


def utc_iso_to_user_time(utc_time_str, user_state):
    """
    Convert a UTC ISO string (from API) to a datetime in the user's timezone.
    """
    user_timezone = get_user_timezone(user_state)
    if isinstance(utc_time_str, datetime):
        dt_utc = utc_time_str
    else:
        dt_utc = datetime.fromisoformat(utc_time_str.replace("Z", "+00:00"))
    return convert_utc_to_local(dt_utc, user_timezone)
