"""
Utility module for managing latency in user-facing operations.
Provides functions to create and manage status update tasks for long-running operations.
"""

import asyncio
import logging
from typing import Any, Callable, Optional

from app.core.logger_setup import logger
from app.state.session_data import UserDataContext


async def create_status_update_task(
    ctx: UserDataContext,
    delay: float = 0.04,  # 40 milliseconds by default
    message: str = "Just a moment while I look that up for you...",
    operation_name: str = "operation",
) -> asyncio.Task:
    """
    Creates an asyncio task that will send a status update message to the user
    after a short delay if the main operation is taking longer than expected.

    Args:
        ctx: The user data context for sending messages
        delay: Time in seconds to wait before sending the status message (default: 0.04s)
        message: The message to send to the user
        operation_name: Name of the operation for logging purposes

    Returns:
        The created asyncio task
    """

    async def _speak_taking_a_moment_message():
        try:
            await asyncio.sleep(delay)
            # Check if the task was cancelled by checking current task's state
            current_task = asyncio.current_task()
            if current_task and not current_task.cancelled():
                logger.info(
                    f"{operation_name}: Taking > {delay*1000:.0f}ms, sending status update."
                )
                await ctx.session.say(message)
        except asyncio.CancelledError:
            logger.info(
                f"{operation_name}: Status update message task was cancelled (operation was fast enough or main task errored)."
            )
        except Exception as e:
            logger.error(f"{operation_name}: Error in status update message task: {e}")

    return asyncio.create_task(_speak_taking_a_moment_message())


async def cleanup_status_task(status_task: Optional[asyncio.Task]) -> None:
    """
    Safely cleans up a status update task if it exists and is not done.

    Args:
        status_task: The asyncio task to clean up
    """
    if status_task and not status_task.done():
        status_task.cancel()
        try:
            # Wait briefly for cancellation to complete, but don't block indefinitely
            await asyncio.wait_for(status_task, timeout=0.1)
        except (asyncio.CancelledError, asyncio.TimeoutError):
            pass  # Expected if cancelled or timed out quickly
        except Exception as e:
            logger.error(f"Error during status task cleanup: {e}")
