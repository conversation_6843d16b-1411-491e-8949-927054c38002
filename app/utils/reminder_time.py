import re
from datetime import date, datetime, time, timedelta, timezone

from dateutil import parser, relativedelta
from dateutil.rrule import (
    DAILY,
    FR,
    MO,
    MONTHLY,
    SA,
    SU,
    TH,
    TU,
    WE,
    WEEKLY,
    YEARLY,
    rrule,
)

# Map day names to rrule constants
day_map = {
    "monday": MO,
    "mon": MO,
    "tuesday": TU,
    "tue": TU,
    "wednesday": WE,
    "wed": WE,
    "thursday": TH,
    "thu": TH,
    "friday": FR,
    "fri": FR,
    "saturday": SA,
    "sat": SA,
    "sunday": SU,
    "sun": SU,
}


def parse_reminder(text, current_time=None):
    """
    Parse reminder text and return a standardized dictionary with:
    - reminder_text: original user input
    - is_recurring: boolean
    - recurrence_rule: Only the RRULE part (e.g., "FREQ=DAILY") or None
    - trigger_time: datetime
    """
    if not current_time:
        current_time = datetime.now(timezone.utc)

    # First parse using existing logic
    parsed_result = _parse_reminder_internal(text, current_time)

    # Extract just the RRULE part if it exists
    recurrence_rule = None
    if parsed_result["rule"]:
        rule_str = str(parsed_result["rule"])
        # Extract just the RRULE part (after "RRULE:")
        if "RRULE:" in rule_str:
            recurrence_rule = rule_str.split("RRULE:")[1].strip()

    data = {
        "reminder_text": text,
        "is_recurring": parsed_result["type"] == "recurring",
        "recurrence_rule": recurrence_rule,
        "trigger_time": parsed_result["trigger_time"],
    }
    print(data)
    return data


def _parse_reminder_internal(text, current_time):
    """Internal parsing function with original logic"""
    result = {
        "type": "once",
        "trigger_time": None,
        "rule": None,
        "human_readable": "",
        "time": None,
    }

    try:
        clean_text = re.sub(
            r"remind me to|set reminder for|at", "", text, flags=re.IGNORECASE
        ).strip()

        recurring_result = parse_recurring_reminder(clean_text, current_time)
        if recurring_result:
            result.update(recurring_result)
            return result

        time_result = parse_one_time_reminder(clean_text, current_time)
        result.update(time_result)
        return result

    except Exception as e:
        raise ValueError(f"Could not parse reminder: {text}. Error: {str(e)}")


def parse_one_time_reminder(text, current_time):
    """Parse one-time reminders"""
    result = {"type": "once", "human_readable": "One-time reminder", "time": None}

    # Extract time components
    time_match = re.search(r"(\d{1,2}(?::\d{2})?\s*(?:am|pm)?)", text, re.IGNORECASE)
    if time_match:
        time_str = time_match.group(1)
        parsed_time = parser.parse(time_str).time()
        result["time"] = parsed_time.strftime("%H:%M")
    else:
        parsed_time = current_time.time()

    # Determine the date
    if "today" in text.lower():
        reminder_date = current_time.date()
        result["human_readable"] = f'Today at {parsed_time.strftime("%I:%M %p")}'
    elif "tomorrow" in text.lower():
        reminder_date = (current_time + timedelta(days=1)).date()
        result["human_readable"] = f'Tomorrow at {parsed_time.strftime("%I:%M %p")}'
    elif "next week" in text.lower():
        reminder_date = (current_time + timedelta(weeks=1)).date()
        result["human_readable"] = f'Next week at {parsed_time.strftime("%I:%M %p")}'
    else:
        # Try to parse the full date
        try:
            dt = parser.parse(text, fuzzy=True)
            reminder_date = dt.date()
            parsed_time = dt.time()
            result["human_readable"] = dt.strftime("%A, %b %d at %I:%M %p")
            result["time"] = parsed_time.strftime("%H:%M")
        except:
            # Default to today if no date specified
            reminder_date = current_time.date()
            result["human_readable"] = f'Today at {parsed_time.strftime("%I:%M %p")}'

    start_dt = datetime.combine(reminder_date, parsed_time)
    result["trigger_time"] = start_dt
    result["rule"] = None  # Changed from YEARLY to None for one-time reminders

    return result


def parse_recurring_reminder(text, current_time):
    """Parse recurring reminders and return None if not a recurring pattern"""
    text = text.lower()

    # Extract time from the text
    time_match = re.search(r"(\d{1,2}(?::\d{2})?\s*(?:am|pm)?)", text)
    if time_match:
        time_str = time_match.group(1)
        parsed_time = parser.parse(time_str).time()
    else:
        parsed_time = current_time.time()

    result = {
        "type": "recurring",
        "time": parsed_time.strftime("%H:%M"),
        "human_readable": "",
        "trigger_time": datetime.combine(current_time.date(), parsed_time),
    }

    # Check for different recurrence patterns
    if "daily" in text or "every day" in text or "everyday" in text:
        result.update(
            {
                "rule": rrule(DAILY, dtstart=result["trigger_time"]),
                "human_readable": f'Daily at {parsed_time.strftime("%I:%M %p")}',
            }
        )
        return result

    elif "weekly" in text or "every week" in text or "everyweek" in text:
        result.update(
            {
                "rule": rrule(WEEKLY, dtstart=result["trigger_time"]),
                "human_readable": f'Weekly on {result["trigger_time"].strftime("%A")} at {parsed_time.strftime("%I:%M %p")}',
            }
        )
        return result

    elif "monthly" in text or "every month" in text or "everymonth" in text:
        result.update(
            {
                "rule": rrule(MONTHLY, dtstart=result["trigger_time"]),
                "human_readable": f'Monthly on day {result["trigger_time"].day} at {parsed_time.strftime("%I:%M %p")}',
            }
        )
        return result

    elif "yearly" in text or "every year" in text or "everyyear" in text:
        result.update(
            {
                "rule": rrule(YEARLY, dtstart=result["trigger_time"]),
                "human_readable": f'Yearly on {result["trigger_time"].strftime("%b %d")} at {parsed_time.strftime("%I:%M %p")}',
            }
        )
        return result

    # Handle specific days of week
    days_found = []
    for day_name, day_const in day_map.items():
        if day_name in text:
            days_found.append(day_const)

    if days_found:
        day_names = [day_map_inverse[d] for d in days_found]
        result.update(
            {
                "rule": rrule(
                    WEEKLY, byweekday=days_found, dtstart=result["trigger_time"]
                ),
                "human_readable": f'Weekly on {", ".join(day_names)} at {parsed_time.strftime("%I:%M %p")}',
            }
        )
        return result

    return None


# Inverse day map for human-readable output
day_map_inverse = {v: k for k, v in day_map.items()}

data = parse_reminder("may 20th at 8 am")
print(data, type(data))
print(type(data["trigger_time"]))
print(data["is_recurring"])
print(data["recurrence_rule"])
# def parse_reminder(reminder_text, current_time=None):
#     """
#     Parses a natural language reminder into a structured format.

#     Args:
#         reminder_text (str): The natural language reminder (e.g., "4 pm tomorrow" or "daily at 9 am").
#         current_time (datetime, optional): The current date and time to use as a reference (UTC, offset-aware).

#     Returns:
#         dict: A dictionary containing the reminder text, recurrence status, recurrence rule, and trigger time.

#     Raises:
#         ValueError: If the reminder text is invalid or cannot be parsed.
#     """
#     import logging

#     logger = logging.getLogger(__name__)

#     # Log the input for debugging
#     logger.info(f"Parsing reminder text: '{reminder_text}'")

#     # Get current time in UTC (offset-aware)
#     if current_time is None:
#         current_time = datetime.now(pytz.UTC)

#     logger.info(f"Current reference time: {current_time.isoformat()}")

#     # Check if the reminder text is empty or too vague
#     if not reminder_text or reminder_text.strip() == "":
#         logger.error("Empty reminder text")
#         raise ValueError(
#             "Please provide a specific time for the reminder, such as '4pm today', 'tomorrow at 9am', or 'every Monday at 8am'."
#         )

#     # Check for common vague time patterns that should be rejected
#     vague_patterns = [
#         "soon",
#         "later",
#         "sometime",
#         "anytime",
#         "whenever",
#         "in a bit",
#         "in a while",
#         "in some time",
#     ]

#     if any(pattern in reminder_text.lower() for pattern in vague_patterns):
#         logger.error(f"Vague time pattern detected in: {reminder_text}")
#         raise ValueError(
#             "Please provide a more specific time for the reminder, such as '4pm today', 'tomorrow at 9am', or 'every Monday at 8am'."
#         )

#     # First try to parse using dateparser for accurate time parsing
#     import dateparser

#     settings = {"RELATIVE_BASE": current_time}

#     # Check if this is an ISO format date string
#     try:
#         if "T" in reminder_text and ("+" in reminder_text or "Z" in reminder_text):
#             # This looks like an ISO format string, try to parse it directly
#             iso_time = (
#                 reminder_text.replace("Z", "+00:00")
#                 if reminder_text.endswith("Z")
#                 else reminder_text
#             )
#             parsed_time = datetime.fromisoformat(iso_time)
#             if parsed_time.tzinfo is None:
#                 parsed_time = pytz.UTC.localize(parsed_time)

#             logger.info(f"Parsed ISO format time: {parsed_time.isoformat()}")

#             # Return non-recurring reminder with the parsed time
#             return {
#                 "reminder_text": reminder_text,
#                 "is_recurring": False,
#                 "recurrence_rule": None,
#                 "trigger_time": parsed_time.isoformat(),
#             }
#     except (ValueError, AttributeError):
#         # Not an ISO format, continue with other parsing methods
#         pass

#     # Try dateparser for natural language parsing
#     dateparser_time = dateparser.parse(reminder_text, settings=settings)

#     # If dateparser couldn't parse the time, provide a helpful error message
#     if not dateparser_time:
#         logger.error(f"Dateparser failed to parse: {reminder_text}")
#         raise ValueError(
#             "I couldn't understand the time format. Please use a clear time format like:\n"
#             "- '4pm today'\n"
#             "- 'tomorrow at 9am'\n"
#             "- 'April 30th at 3pm'\n"
#             "- 'every day at 8am'\n"
#             "- 'every Monday at 10am'"
#         )

#     # Make sure it's timezone-aware
#     if dateparser_time.tzinfo is None:
#         dateparser_time = pytz.UTC.localize(dateparser_time)

#     logger.info(f"Dateparser parsed time: {dateparser_time.isoformat()}")

#     # Check for recurring keywords
#     recurring_keywords = [
#         "daily",
#         "weekly",
#         "monthly",
#         "yearly",
#         "every day",
#         "every week",
#         "every month",
#         "every year",
#         "each day",
#         "each week",
#         "each month",
#         "each year",
#         "every monday",
#         "every tuesday",
#         "every wednesday",
#         "every thursday",
#         "every friday",
#         "every saturday",
#         "every sunday",
#         "mondays",
#         "tuesdays",
#         "wednesdays",
#         "thursdays",
#         "fridays",
#         "saturdays",
#         "sundays",
#     ]

#     is_recurring = any(
#         keyword in reminder_text.lower() for keyword in recurring_keywords
#     )

#     if is_recurring:
#         # Use RecurringEvent for recurring reminders
#         r = RecurringEvent(now_date=current_time)
#         parsed = r.parse(reminder_text)

#         if parsed:
#             logger.info(f"RecurringEvent parsed: {parsed}")

#             # Extract DTSTART if present
#             dtstart_lines = [
#                 line for line in parsed.split("\n") if line.startswith("DTSTART:")
#             ]
#             if dtstart_lines:
#                 dtstart_str = dtstart_lines[0].replace("DTSTART:", "")
#                 try:
#                     dtstart = datetime.strptime(dtstart_str, "%Y%m%dT%H%M%S")
#                     dtstart = pytz.UTC.localize(dtstart)
#                 except ValueError:
#                     dtstart = (
#                         dateparser_time  # Use the dateparser time if DTSTART is invalid
#                     )
#             else:
#                 dtstart = (
#                     dateparser_time  # Use the dateparser time if DTSTART is missing
#                 )

#             # Parse RRULE with timezone-aware dtstart
#             try:
#                 rrule_obj = rrulestr(parsed, dtstart=dtstart)
#                 next_occurrence = rrule_obj.after(current_time)

#                 # Extract the RRULE part from the parsed string
#                 rrule_lines = [
#                     line for line in parsed.split("\n") if line.startswith("RRULE:")
#                 ]
#                 rrule_str = rrule_lines[0].replace("RRULE:", "") if rrule_lines else ""

#                 logger.info(
#                     f"Recurring reminder next occurrence: {next_occurrence.isoformat() if next_occurrence else None}"
#                 )

#                 return {
#                     "reminder_text": reminder_text,
#                     "is_recurring": True,
#                     "recurrence_rule": rrule_str,
#                     "trigger_time": (
#                         next_occurrence.isoformat()
#                         if next_occurrence
#                         else dateparser_time.isoformat()
#                     ),
#                 }
#             except Exception as e:
#                 logger.error(f"Failed to parse recurring reminder with rrulestr: {e}")
#                 # Fall back to dateparser time for recurring reminders
#                 return {
#                     "reminder_text": reminder_text,
#                     "is_recurring": True,
#                     "recurrence_rule": "FREQ=DAILY",  # Default to daily
#                     "trigger_time": dateparser_time.isoformat(),
#                 }
#         else:
#             logger.warning(f"RecurringEvent failed to parse: {reminder_text}")
#             # Fall back to dateparser time for recurring reminders
#             return {
#                 "reminder_text": reminder_text,
#                 "is_recurring": True,
#                 "recurrence_rule": "FREQ=DAILY",  # Default to daily
#                 "trigger_time": dateparser_time.isoformat(),
#             }
#     else:
#         # Non-recurring reminder with dateparser time
#         logger.info(f"Non-recurring reminder time: {dateparser_time.isoformat()}")

#         # Check if the parsed time is too far in the past (more than a day)
#         # This might indicate a parsing error
#         if dateparser_time < (current_time - timedelta(days=1)):
#             logger.warning(
#                 f"Parsed time is more than a day in the past: {dateparser_time.isoformat()}"
#             )
#             raise ValueError(
#                 "The time you provided seems to be in the past. Please specify a future time like:\n"
#                 "- '4pm today'\n"
#                 "- 'tomorrow at 9am'\n"
#                 "- 'next Monday at 10am'"
#             )

#         # If the time is slightly in the past (less than a day), adjust to tomorrow
#         if dateparser_time < current_time:
#             dateparser_time = dateparser_time + timedelta(days=1)
#             logger.info(f"Adjusted time to tomorrow: {dateparser_time.isoformat()}")

#         return {
#             "reminder_text": reminder_text,
#             "is_recurring": False,
#             "recurrence_rule": None,
#             "trigger_time": dateparser_time.isoformat(),
#         }


# examples = [
#     "set reminder time is 4 pm tomorrow",
#     "remind me at 8 am today",
#     "next week at 3:30 pm",
#     "remind me daily at 7 am",
#     "set reminder every monday and wednesday at 9 am",
#     "remind me monthly on the 15th at 10 am",
#     "yearly reminder on june 5 at 2 pm",
#     "every tuesday at 3 pm",
#     "remind me every day at 8:30 am",
#     "set reminder for friday at 5 pm",
#     "on 5 pm tomorrow",
#     "hmm like set on 5 pm evening today",
# ]

# for example in examples:
#     print("-" * 50)
#     try:
#         result = parse_reminder(example)
#         print(f"Input: '{example}'")
#         # print(f"Type: {result['type']}")
#         print(f"Start: {result['trigger_time']}")
#         print(f"RRULE: {result.get('is_recurring', False)}")
#         print(f"Rule: {result['recurrence_rule']}")
#         print(f"Human readable: {result['reminder_text']}")
#         # print("-" * 50)
#     except ValueError as e:
#         # print("-" * 50)
#         print(f"Error parsing '{example}': {e}")
