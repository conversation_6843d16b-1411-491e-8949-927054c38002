import json

from livekit import rtc

from app.constants.action_types import ActionStatus, ActionType, ErrorCode


async def send_event(
    room: rtc.Room,
    action_type: ActionType,
    message: str,
    data: dict = None,
    status: ActionStatus = ActionStatus.SUCCESS,
    error: dict = None,
):
    """
    Send an event to the frontend via WebRTC data channel.

    Args:
        action_type: The type of action (from ActionType enum)
        message: User-friendly message about the action
        data: Dictionary containing action-specific data
        status: Status of the action (from ActionStatus enum)
        error: Error information if status is not SUCCESS

    Returns:
        bool: True if the event was sent successfully, False otherwise
    """
    if not room:
        print("Error: Room context not available for sending event")
        return False

    # Create the payload
    payload = {
        "actionType": action_type.value,
        "message": message,
        "data": data or {},
        "status": status.value,
        "error": error,
    }

    payload_str = json.dumps(payload)

    try:
        # # Send the event via both methods for compatibility
        # await room.local_participant.send_text(payload_str, topic="EVENT_TRIGGER")
        await room.local_participant.publish_data(
            payload=payload_str, topic="EVENT_TRIGGER"
        )

        print(f"\n\nEvent sent: {action_type.value}, data: {data}")
        return True
    except Exception as e:
        print(f"Error sending event: {str(e)}")
        return False
