voice_samples = [
    {
        "name": "Fable",
        "gender": "male",
        "language": "english",
        "provider": "option1",
        "voice_id": "fable",
        "category": "premade",
        "preview_url": "https://cdn.openai.com/API/docs/audio/fable.wav",
        "is_public": True,
    },
    {
        "_id": {"$oid": "6698ecdcd12a876a36d07866"},
        "name": "Shimmer",
        "gender": "female",
        "language": "english",
        "provider": "option1",
        "voice_id": "shimmer",
        "category": "premade",
        "preview_url": "https://cdn.openai.com/API/docs/audio/shimmer.wav",
        "is_public": True,
    },
    {
        "_id": {"$oid": "6698eceed12a876a36d07868"},
        "name": "Alloy",
        "gender": "male",
        "language": "english",
        "provider": "option1",
        "voice_id": "alloy",
        "category": "premade",
        "preview_url": "https://cdn.openai.com/API/docs/audio/alloy.wav",
        "is_public": True,
    },
    {
        "_id": {"$oid": "6698eccdd12a876a36d07865"},
        "name": "Onyx",
        "gender": "male",
        "language": "english",
        "provider": "option1",
        "voice_id": "onyx",
        "category": "premade",
        "preview_url": "https://cdn.openai.com/API/docs/audio/onyx.wav",
        "is_public": True,
    },
    {
        "_id": {"$oid": "6698ecf7d12a876a36d07869"},
        "name": "Nova",
        "gender": "female",
        "language": "english",
        "provider": "option1",
        "voice_id": "nova",
        "category": "premade",
        "preview_url": "https://cdn.openai.com/API/docs/audio/nova.wav",
        "is_public": True,
    },
    {
        "_id": {"$oid": "6698ece5d12a876a36d07867"},
        "name": "Echo",
        "gender": "male",
        "language": "english",
        "provider": "option1",
        "voice_id": "echo",
        "category": "premade",
        "preview_url": "https://cdn.openai.com/API/docs/audio/echo.wav",
        "is_public": True,
    },
]
