# MyHoudini

**Version:** 1.0

## 1. Project Overview

MyHoudini is a voice-activated AI assistant designed to help users manage personal information, schedules, and reminders. [cite: 1] The backend (Python/MongoDB) focuses on providing the data management and logic to support a user-friendly voice interface. The target user may benefit from memory assistance. [cite: 1]

### 1.1. Project Structure

The MyHoudini project is split into two main components:

1. **API Server** (`api_myhoudini`): Handles data storage and retrieval using FastAPI and MongoDB
2. **Agent Server** (`poc_myhoudini`): Handles voice assistant functionality using LiveKit and OpenAI

```
/
├── api_myhoudini/         # API server
│   ├── app/
│   │   ├── core/          # Core functionality
│   │   ├── database/      # Database models and connections
│   │   ├── models/        # Data models
│   │   └── routers/       # API endpoints
│   └── main.py            # API server entry point
│
└── poc_myhoudini/         # Agent server
    ├── app/
    │   ├── api_client.py  # Client for API server
    │   ├── prompts/       # Assistant prompts
    │   ├── providers/     # TTS providers
    │   ├── samples/       # Voice samples
    │   ├── services/      # Service layer
    │   └── state/         # State management
    └── run.py             # Agent server entry point
```

## 2. Core Feature Checklist

The following functionalities are core to the MyHoudini backend:

- **User Onboarding:**
  - Capture user's provided and preferred names. [cite: 3, 4, 5]
  - Support voice selection (TTS options). [cite: 6, 7]
  - Onboard new users by informing them of activation phrases (“Hey Houdini”). [cite: 8]
  - Guide users through initial data setup (contacts). [cite: 9, 10, 11, 12, 13]
- **Voice Activation:**
  - Listen for the activation phrase “Hey Houdini”. [cite: 8]
- **Contact Management (CRUD):**
  - Store contact information: Name, Relationship, Details (notes), Phone, Email, Address (structured).
  - Implement a reminder flag for adding contact details later. [cite: 13, 14]
- **Appointment Scheduling (CRUD):**
  - Link appointments to contacts.
  - Store appointment details: Date/Time, Location (optional), Title. [cite: 15, 16]
  - Integrate with native calendar app (Read/Write - See INT-01).
- **Reminder System (CRUD):**
  - Support various reminder types: Appointment, Medication, GeneralTask, Weather.
  - Integrate with native reminder app (Read/Write - See INT-02).
- **Information Recall / “Memory” (CRUD):**
  - Store text snippets/notes related to contacts or appointments.
  - Enable querying of stored information (e.g., "What do you remember about X?"). [cite: 21, 22, 23]
  - Allow users to update, delete, and control stored memories.
- **Proactive Assistance:**
  - Provide pre-appointment contact summaries. [cite: 18, 19, 20]
  - Prompt for notes/follow-up actions post-appointment. [cite: 24]
- **General Assistant Features:**
  - Handle user queries:
    - Weather (API call) [cite: 27]
    - News (API call) [cite: 27]
    - General Facts (LLM) [cite: 27, 28, 29]
  - Implement response guardrails. [cite: 29]
  - Handle internal navigation/help requests. [cite: 30, 31, 32]

## 3. Data Model Overview (MongoDB Collections)

- **UserProfile:** Stores user preferences (name, voice), default settings (reminder timing), daily weather configuration, and timestamps.
- **Contacts:** Stores contact details, relationship, notes, contact methods, structured address, and timestamps. Includes a userId link.
- **Appointments:** Stores appointment title, dateTime, location, linked contactId (optional), notes, status, externalCalendarEventId (optional), and timestamps. Includes a userId link.
- **Reminders:** Stores reminder type, reminderDateTime, message, status, related IDs (appointmentId, contactId), recurrenceRule (optional), externalReminderId (optional), and timestamps. Includes a userId link.
- **Memories / Notes:** Stores text snippets, context, and linked IDs (contactId, appointmentId), and timestamps. Could be embedded in Contacts/Appointments or a separate collection. Includes a userId link.

  - Primary Key for all collections: `_id` (MongoDB ObjectId)
  - Use BSON Date type for all timestamps.

## 4. Technology Stack

- **Backend Language:** Python
- **Database:** MongoDB
- **API Framework:** FastAPI
- **Real-time Comms / Framework:** LiveKit
- **General LLM:** Google Gemini API
- **Speech-to-Text (STT):** Deepgram
- **Text-to-Speech (TTS):** ElevenLabs / OpenAI (Provide choice)
- **Voice AI LLM (Specific Tasks):** OpenAI API
- **HTTP Client:** Requests

## 5. API / Integration Points

### 5.1. External APIs

- Native Device Calendar API (Read/Write Appointments)
- Native Device Reminders/Tasks API (Read/Write Reminders)
- Weather Service API (Fetch forecasts)
- News Service API (Fetch headlines/summaries)
- Deepgram API (STT)
- ElevenLabs / OpenAI API (TTS)
- Google Gemini API (General LLM Queries)
- OpenAI API (Voice AI Tasks)
- LiveKit (Real-time voice handling)

### 5.2. Internal API Server

The MyHoudini project uses a separate API server (`api_myhoudini`) for data operations. The agent server (`poc_myhoudini`) communicates with the API server using the requests library.

#### API Endpoints

##### Users

- `POST /api/users` - Create a new user
- `GET /api/users/{user_id}` - Get a user by ID
- `GET /api/users/by-voice/{voice_id}` - Get a user by voice ID
- `GET /api/users/by-participant/{participant_id}` - Get a user by participant ID
- `GET /api/users` - Get all users
- `PUT /api/users/{user_id}` - Update a user
- `DELETE /api/users/{user_id}` - Delete a user

##### Contacts

- `POST /api/contacts` - Create a new contact
- `GET /api/contacts/{contact_id}` - Get a contact by ID
- `GET /api/contacts/user/{user_id}` - Get contacts by user ID
- `GET /api/contacts` - Get all contacts
- `PUT /api/contacts/{contact_id}` - Update a contact
- `DELETE /api/contacts/{contact_id}` - Delete a contact

##### Voices

- `POST /api/voices` - Create a new voice
- `GET /api/voices/{voice_id}` - Get a voice by ID
- `GET /api/voices/by-name/{voice_name}` - Get a voice by name
- `GET /api/voices` - Get all voices
- `PUT /api/voices/{voice_id}` - Update a voice
- `DELETE /api/voices/{voice_id}` - Delete a voice

##### Tokens

- `GET /api/tokens/livekit` - Generate a LiveKit token

## 6. Setup and Running

### 6.1. API Server

1. Navigate to the API server directory:

   ```bash
   cd api_myhoudini
   ```

2. Create a virtual environment:

   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install dependencies:

   ```bash
   pip install -r requirements.txt
   ```

4. Create a `.env` file with the following content:

   ```
   MONGO_URI=mongodb://localhost:27017
   DATABASE_NAME=myhoudini
   ```

5. Start the API server:
   ```bash
   uvicorn main:app --reload
   ```

### 6.2. Agent Server

1. Navigate to the agent server directory:

   ```bash
   cd poc_myhoudini
   ```

2. Create a virtual environment:

   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install dependencies:

   ```bash
   pip install -r requirements.txt
   ```

4. Create a `.env.local` file with the following content:

   ```
   API_ENDPOINT=http://localhost:8000
   LIVEKIT_URL=<your LiveKit server URL>
   LIVEKIT_API_KEY=<your API Key>
   LIVEKIT_API_SECRET=<your API Secret>
   OPENAI_API_KEY=<your OpenAI API key>
   DEEPGRAM_API_KEY=<your Deepgram API key>
   ```

5. Start the agent server:
   ```bash
   python run.py
   ```

## 7. Key Considerations

- **Voice Interface:** Low latency and high accuracy for STT/TTS are crucial. Smoothly handle conversational flow, confirmations, and corrections.
- **Modularity:** Code should be structured for maintainability, especially around interactions with external services (TTS, STT, LLM, DB).
- **Error Handling:** Implement robust error handling for all external API calls and integrations. Provide user-friendly feedback on failures.
- **Configuration:** Enable easy configuration of API keys, voice choices, and other settings.

## 8. Schema for MyHoudini Data

### 8.1. User Profile

- **UserID:** UUID (Primary Key)
- **ProvidedName:** String
- **PreferredName:** String
- **AssistantVoice:** String
- **DefaultReminderTiming:** Object or String (e.g., `{ value: 1, unit: 'hour' }`, "1 day before", "custom")
  - _Alternative:_ `DefaultReminderValue`: Integer, `DefaultReminderUnit`: Enum (minute, hour, day, week)
- **RemindAboutPersonBeforeMeeting:** Boolean (Default for proactive reminders)
- **IsInitialSetupComplete:** Boolean
- **SetupReminderTimestamp:** Timestamp (Optional: For paused setup)
- **LastInteractionTimestamp:** Timestamp

### 8.2. Contacts (People)

- **ContactID:** UUID (Primary Key)
- **UserID:** UUID (Foreign Key)
- **Name:** String
- **Email:** String
- **Address:** String
- **Relationship:** Enum (Parent, Spouse, Child, Grandchild, GreatGrandchild, Friend, Neighbor, Doctor, Other)
- **IsAlive:** Boolean (For family)
- **Details:** Text (Hobbies, etc.)
- **PreferredContactMethod:** Enum (Phone, Text, Email, Other, Unknown)
- **ContactValue:** String (Phone number, email, etc.)
- **NeedsDetailUpdateReminder:** Boolean
- **CreationTimestamp:** Timestamp
- **LastUpdatedTimestamp:** Timestamp

### 8.3. Appointments

- **AppointmentID:** UUID (Primary Key)
- **UserID:** UUID (Foreign Key)
- **ContactID:** UUID (Optional: Foreign Key)
- **Title:** String
- **DateTime:** Timestamp
- **Location:** String (Optional)
- **ReminderTiming:** Object or String (Overrides user default)
  - _Alternative:_ `ReminderValue`: Integer, `ReminderUnit`: Enum (minute, hour, day, week)
- **RemindAboutPerson:** Boolean (Overrides user default)
- **Status:** Enum (Scheduled, Completed, Cancelled, PendingFollowUp)
- **PostMeetingNotes:** Text
- **RequiresFollowUp:** Boolean
- **FollowUpTaskID:** UUID (Optional: Foreign Key to Reminders)
- **CreationTimestamp:** Timestamp

### 8.4. Reminders

- **ReminderID:** UUID (Primary Key)
- **UserID:** UUID (Foreign Key)
- **ReminderType:** Enum (Appointment, UpdateContactDetails, GeneralTask, FollowUp)
- **RelatedAppointmentID:** UUID (Optional: Foreign Key)
- **RelatedContactID:** UUID (Optional: Foreign Key)
- **ReminderDateTime:** Timestamp
- **Message:** String
- **Status:** Enum (Pending, Triggered, Dismissed, Snoozed)
- **SnoozeUntilTimestamp:** Timestamp (Optional: If snoozed)
- **CreationTimestamp:** Timestamp

### 8.5. Memories / Notes (Associated Information)

- _Option 1: Integrated into Contacts (Details) and Appointments (PostMeetingNotes)_
- _Option 2: Separate Entity_
  - **MemoryID:** UUID (Primary Key)
  - **UserID:** UUID (Foreign Key)
  - **RelatedContactID:** UUID (Optional: Foreign Key)
  - **RelatedAppointmentID:** UUID (Optional: Foreign Key)
  - **MemoryText:** Text
  - **Context:** String (Optional)
  - **Status:** Enum (Active, Archived, MarkedForDeletion)
  - **CreationTimestamp:** Timestamp
  - **LastUpdatedTimestamp:** Timestamp

### 8.6. Interaction Log (Optional but Recommended)

- **LogID:** UUID (Primary Key)
- **UserID:** UUID (Foreign Key)
- **Timestamp:** Timestamp
- **UserInput:** Text
- **AssistantResponse:** Text
- **IntentDetected:** String (e.g., SetAppointment, QueryContact, GeneralQuestion, UpdateMemory)
- **EntitiesExtracted:** JSON or Text (e.g., `{ "person": "Tom", "date": "today" }`)

**Key Schema Considerations:**

- **Relationships:** Foreign keys link data.
- **User Preferences:** UserProfile stores defaults.
- **Contact Details:** Contacts table stores people information.
- **Appointments & Reminders:** Separate tables for events and notifications.
- **Memory Management:** Memories/Notes for information retrieval and proactive assistance.
- **Flexibility:** UUIDs for unique IDs, Enums for controlled vocabularies, Text fields for notes.
