import os

from dotenv import load_dotenv

# Load environment variables from the .env file (if present)
load_dotenv(override=True)

# # Constants

# Legacy database settings (will be removed)
MONGO_URI: str = os.getenv(("MONGO_URI"))
DATABASE_NAME: str = os.getenv("DATABASE_NAME")

VOICE_OPTIONS: list = ["alloy", "echo", "fable", "onyx", "nova"]
DEFAULT_VOICE: str = "alloy"
DEFAULT_PROVIDER = "openai"
DEFAULT_DAILY_UPDATES_TIME: str = "08:00"  # Default daily-updates time
DEFAULT_QUIT_MODE: bool = False  # Default quiet mode
DEFAULT_QUIT_MODE_END: str = "08:00"  # Default quiet mode end time

# Collection names
USER_COLLECTION: str = "users"
VOICE_COLLECTION: str = "voices"
CONTACT_COLLECTION: str = "contacts"
APPOINTMENT_COLLECTION: str = "appointments"
REMINDER_COLLECTION: str = "reminders"
MEMORY_COLLECTION: str = "memories"

# Legacy collection name (for backward compatibility)
USER_PROFILE: str = "user_profiles"

# Voice mapping: original TTS voice name -> display name
VOICE_MAPPING = {
    "alloy": "mira",
    "echo": "James",
    "fable": "Diane",
    "onyx": "Douglas",
    "nova": "Sandra",
}

DEBUGE_VAR = os.getenv(("DEBUGE_VAR"))
