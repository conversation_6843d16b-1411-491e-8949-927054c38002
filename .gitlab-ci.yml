stages:
  - build_and_deploy

build_and_deploy:
  stage: build_and_deploy
  image: ubuntu:latest
  script:
    - apt-get update && apt-get install -y openssh-client
    - mkdir /root/.ssh
    - echo "$SSH_PRIVATE_KEY_DEV" | tr -d '\r' > /root/.ssh/id_rsa
    - chmod 400 /root/.ssh/id_rsa
    - ssh-keyscan -t rsa ********* >> ~/.ssh/known_hosts
    - scp -i "/root/.ssh/id_rsa" -o StrictHostKeyChecking=no ./deploy_script.sh ranjith093@*********:./
    - ssh -i "/root/.ssh/id_rsa" -o StrictHostKeyChecking=no ranjith093@********* "bash -s" < ./deploy_script.sh "${CI_PROJECT_NAME}" "${CI_COMMIT_SHORT_SHA}" "$CI_COMMIT_REF_NAME"

  only:
    - dev

