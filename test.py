from datetime import datetime

import dateparser
from dateutil.rrule import DAILY, FR, MO, MONTHLY, SA, SU, TH, TU, WE, WEEKLY, YEARLY

WEEKDAYS = {
    "monday": MO,
    "tuesday": TU,
    "wednesday": WE,
    "thursday": TH,
    "friday": FR,
    "saturday": SA,
    "sunday": SU,
}


def parse_user_reminder(user_text: str, is_recurring: bool, base_time=None):
    base_time = base_time or datetime.now()
    text_lower = user_text.lower()
    parsed_time = dateparser.parse(user_text, settings={"RELATIVE_BASE": base_time})

    if is_recurring:
        # Specific days of the week
        weekdays = [const for name, const in WEEKDAYS.items() if name in text_lower]
        if weekdays:
            return {
                "is_recurring": True,
                "rrule": {
                    "freq": WEEKLY,
                    "byweekday": weekdays,
                    "dtstart": parsed_time or base_time,
                },
                "description": f"Weekly on {', '.join([k.capitalize() for k in WEEKDAYS if WEEKDAYS[k] in weekdays])}",
            }

        # Daily
        if "daily" in text_lower or "every day" in text_lower:
            return {
                "is_recurring": True,
                "rrule": {"freq": DAILY, "dtstart": parsed_time or base_time},
                "description": "Daily reminder",
            }

        # Monthly
        if "monthly" in text_lower or "every month" in text_lower:
            return {
                "is_recurring": True,
                "rrule": {"freq": MONTHLY, "dtstart": parsed_time or base_time},
                "description": "Monthly reminder",
            }

        # Yearly
        if (
            "yearly" in text_lower
            or "every year" in text_lower
            or "each year" in text_lower
        ):
            return {
                "is_recurring": True,
                "rrule": {"freq": YEARLY, "dtstart": parsed_time or base_time},
                "description": "Yearly reminder",
            }

        # Biweekly
        if "every other week" in text_lower or "every 2 weeks" in text_lower:
            return {
                "is_recurring": True,
                "rrule": {
                    "freq": WEEKLY,
                    "interval": 2,
                    "dtstart": parsed_time or base_time,
                },
                "description": "Biweekly reminder",
            }

        # Every 2 days
        if "every 2 days" in text_lower:
            return {
                "is_recurring": True,
                "rrule": {
                    "freq": DAILY,
                    "interval": 2,
                    "dtstart": parsed_time or base_time,
                },
                "description": "Every 2 days reminder",
            }

        # Fallback
        if parsed_time:
            return {
                "is_recurring": True,
                "rrule": {"freq": DAILY, "dtstart": parsed_time},
                "description": "Recurring reminder (defaulted to daily)",
            }

        return {
            "is_recurring": True,
            "error": "Could not determine recurrence details",
            "description": "Recurring reminder not understood",
        }

    else:
        if parsed_time:
            return {
                "is_recurring": False,
                "time": parsed_time,
                "description": "One-time reminder",
            }
        else:
            return {
                "is_recurring": False,
                "error": "Could not parse one-time reminder time",
                "description": "Failed to parse",
            }


# Example usage
if __name__ == "__main__":
    current_time = datetime.utcnow()  # April 23, 2025, 03:37 PM IST

    # One-time reminder
    reminder1 = "4 pm tomorrow"
    result1 = parse_reminder("sunday 7PM")
    print(
        result1
    )  # Expected: {'type': 'one-time', 'datetime': datetime(2025, 4, 24, 16, 0)}
    print("\n\n")
    # Recurring reminder
    reminder2 = "daily at 9 am"
    result2 = parse_reminder(reminder2)
    print(
        result2
    )  # Expected: {'type': 'recurring', 'rrule': <rrule object>, 'next_occurrence': datetime(2025, 4, 24, 9, 0)}
    print("\n\n")
    # Another recurring reminder
    reminder3 = "every Monday at 2 pm"
    result3 = parse_reminder(reminder3)
    print(
        result3
    )  # Expected: {'type': 'recurring', 'rrule': <rrule object>, 'next_occurrence': datetime(2025, 4, 28, 14, 0)}
